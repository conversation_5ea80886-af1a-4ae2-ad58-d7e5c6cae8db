interface ContactSectionProps {
  contactRef: React.RefObject<HTMLDivElement | null>;
}

export default function ContactSection({ contactRef }: ContactSectionProps) {
  return (
    <div ref={contactRef} className="mt-20">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4 font-orbitron">
            Contact Us
          </h2>
          <p className="text-lg text-gray-300 font-sora leading-relaxed">
            Ready to start your next project? Get in touch with our team.
          </p>
        </div>

        <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-xl rounded-3xl p-8 md:p-12 border border-gray-700/30 shadow-2xl">
          <form className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Name Field */}
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2 font-sora">
                  Full Name
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#302cff] focus:border-transparent transition-all duration-300 font-sora"
                  placeholder="Enter your full name"
                />
              </div>

              {/* Email Field */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2 font-sora">
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#302cff] focus:border-transparent transition-all duration-300 font-sora"
                  placeholder="Enter your email address"
                />
              </div>
            </div>

            {/* Subject Field */}
            <div>
              <label htmlFor="subject" className="block text-sm font-medium text-gray-300 mb-2 font-sora">
                Subject
              </label>
              <input
                type="text"
                id="subject"
                name="subject"
                className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#302cff] focus:border-transparent transition-all duration-300 font-sora"
                placeholder="What's this about?"
              />
            </div>

            {/* Message Field */}
            <div>
              <label htmlFor="message" className="block text-sm font-medium text-gray-300 mb-2 font-sora">
                Message
              </label>
              <textarea
                id="message"
                name="message"
                rows={6}
                className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#302cff] focus:border-transparent transition-all duration-300 font-sora resize-none"
                placeholder="Tell us about your project or inquiry..."
              ></textarea>
            </div>

            {/* Submit Button */}
            <div className="text-center pt-4">
              <button
                type="submit"
                className="px-8 py-4 bg-gradient-to-r from-[#302cff] to-purple-600 text-white font-bold font-sora rounded-xl shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-[#302cff]/30"
              >
                Send Message
              </button>
            </div>
          </form>

          {/* Contact Info */}
          <div className="mt-12 pt-8 border-t border-gray-700/50">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 text-center md:text-left">
              <div>
                <h4 className="text-lg font-bold text-white mb-3 font-orbitron">Email Us</h4>
                <p className="text-gray-300 font-sora"><EMAIL></p>
              </div>
              <div>
                <h4 className="text-lg font-bold text-white mb-3 font-orbitron">Call Us</h4>
                <p className="text-gray-300 font-sora">+201112628619</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
