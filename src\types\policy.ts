export interface PolicyData {
  id: number;
  title_en: string | null;
  title_ar: string | null;
  subtitle_en: string | null;
  subtitle_ar: string | null;
  description_en: string[] | null;
  description_ar: string[] | null;
  type: string;
  index: number;
  status: string;
  createdAt: string;
  updatedAt: string;
}

export interface PolicyResponse {
  data: PolicyData[];
}

// Terms data structure (assuming same structure as policies)
export interface TermsData {
  id: number;
  title_en: string | null;
  title_ar: string | null;
  subtitle_en: string | null;
  subtitle_ar: string | null;
  description_en: string[] | null;
  description_ar: string[] | null;
  type: string;
  index: number;
  status: string;
  createdAt: string;
  updatedAt: string;
}

export interface TermsResponse {
  data: TermsData[];
}

export type Language = 'en' | 'ar';
