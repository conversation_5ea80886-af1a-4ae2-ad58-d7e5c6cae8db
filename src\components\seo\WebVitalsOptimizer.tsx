'use client';

import { useEffect } from 'react';

export default function WebVitalsOptimizer() {
  useEffect(() => {
    // Core Web Vitals optimizations
    const optimizeWebVitals = () => {
      // 1. Optimize Largest Contentful Paint (LCP)
      const optimizeLCP = () => {
        // Preload critical resources
        const criticalResources = [
          '/fonts/Orbitron-Regular.woff2',
          '/fonts/Sora-Regular.woff2',
          '/assets/BlueLogo.png'
        ];

        criticalResources.forEach(resource => {
          const link = document.createElement('link');
          link.rel = 'preload';
          link.href = resource;
          
          if (resource.includes('.woff2')) {
            link.as = 'font';
            link.type = 'font/woff2';
            link.crossOrigin = 'anonymous';
          } else if (resource.includes('.png') || resource.includes('.jpg') || resource.includes('.webp')) {
            link.as = 'image';
          }
          
          document.head.appendChild(link);
        });

        // Optimize images with intersection observer
        const images = document.querySelectorAll('img[data-src]') as NodeListOf<HTMLImageElement>;
        const imageObserver = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement;
              img.src = img.dataset.src || '';
              img.removeAttribute('data-src');
              imageObserver.unobserve(img);
            }
          });
        }, {
          rootMargin: '50px 0px',
          threshold: 0.01
        });

        images.forEach(img => imageObserver.observe(img));
      };

      // 2. Optimize First Input Delay (FID)
      const optimizeFID = () => {
        // Break up long tasks (utility function for future use)
        // const yieldToMain = () => {
        //   return new Promise(resolve => {
        //     setTimeout(resolve, 0);
        //   });
        // };

        // Defer non-critical JavaScript
        const deferNonCriticalJS = () => {
          const scripts = document.querySelectorAll('script[data-defer]') as NodeListOf<HTMLScriptElement>;
          scripts.forEach(script => {
            const newScript = document.createElement('script');
            newScript.src = script.getAttribute('src') || '';
            newScript.defer = true;
            document.head.appendChild(newScript);
            script.remove();
          });
        };

        // Use requestIdleCallback for non-critical work
        if ('requestIdleCallback' in window) {
          requestIdleCallback(deferNonCriticalJS);
        } else {
          setTimeout(deferNonCriticalJS, 1);
        }
      };

      // 3. Optimize Cumulative Layout Shift (CLS)
      const optimizeCLS = () => {
        // Add size attributes to images without them
        const images = document.querySelectorAll('img:not([width]):not([height])') as NodeListOf<HTMLImageElement>;
        images.forEach(img => {
          // Set default dimensions to prevent layout shift
          if (!img.style.width && !img.style.height) {
            img.style.aspectRatio = '16/9';
            img.style.width = '100%';
            img.style.height = 'auto';
          }
        });

        // Reserve space for dynamic content
        const dynamicElements = document.querySelectorAll('[data-dynamic]') as NodeListOf<HTMLElement>;
        dynamicElements.forEach(element => {
          if (!element.style.minHeight) {
            element.style.minHeight = '200px';
          }
        });

        // Optimize font loading to prevent FOIT/FOUT
        const fontFaces = [
          {
            family: 'Orbitron',
            src: '/fonts/Orbitron-Regular.woff2',
            weight: '400',
            display: 'swap'
          },
          {
            family: 'Sora',
            src: '/fonts/Sora-Regular.woff2',
            weight: '400',
            display: 'swap'
          }
        ];

        fontFaces.forEach(font => {
          const fontFace = new FontFace(font.family, `url(${font.src})`, {
            weight: font.weight,
            display: font.display as FontDisplay
          });
          
          fontFace.load().then(() => {
            document.fonts.add(fontFace);
          }).catch(err => {
            console.warn('Font loading failed:', err);
          });
        });
      };

      // 4. Optimize Time to First Byte (TTFB)
      const optimizeTTFB = () => {
        // Add service worker for caching
        if ('serviceWorker' in navigator) {
          navigator.serviceWorker.register('/sw.js').catch(err => {
            console.warn('Service worker registration failed:', err);
          });
        }

        // Implement resource hints
        const resourceHints = [
          { rel: 'dns-prefetch', href: 'https://fonts.googleapis.com' },
          { rel: 'dns-prefetch', href: 'https://fonts.gstatic.com' },
          { rel: 'preconnect', href: 'https://prod.spline.design' },
        ];

        resourceHints.forEach(hint => {
          const link = document.createElement('link');
          link.rel = hint.rel;
          link.href = hint.href;
          if (hint.rel === 'preconnect') {
            link.crossOrigin = 'anonymous';
          }
          document.head.appendChild(link);
        });
      };

      // Execute optimizations
      optimizeLCP();
      optimizeFID();
      optimizeCLS();
      optimizeTTFB();
    };

    // Run optimizations after DOM is loaded
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', optimizeWebVitals);
    } else {
      optimizeWebVitals();
    }

    // Monitor Core Web Vitals
    const monitorWebVitals = () => {
      if ('PerformanceObserver' in window) {
        // Monitor LCP
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          console.log('LCP:', lastEntry.startTime);
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

        // Monitor FID
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach(entry => {
            const fidEntry = entry as PerformanceEventTiming;
            if (fidEntry.processingStart) {
              console.log('FID:', fidEntry.processingStart - fidEntry.startTime);
            }
          });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });

        // Monitor CLS
        const clsObserver = new PerformanceObserver((list) => {
          let clsValue = 0;
          const entries = list.getEntries();
          entries.forEach(entry => {
            const layoutShiftEntry = entry as PerformanceEntry & { hadRecentInput?: boolean; value?: number };
            if (!layoutShiftEntry.hadRecentInput) {
              clsValue += layoutShiftEntry.value || 0;
            }
          });
          console.log('CLS:', clsValue);
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
      }
    };

    // Start monitoring in development
    if (process.env.NODE_ENV === 'development') {
      monitorWebVitals();
    }

    return () => {
      // Cleanup observers if needed
    };
  }, []);

  return null; // This component doesn't render anything
}
