"use client";

import { useGetInTouch } from "../hooks/useGetInTouch";
import { SECTION_CONTENT, CONTACT_METHODS, FAQ_DATA } from "../consts/getInTouchConsts";

export default function GetInTouch() {
  const {
    refs: { sectionRef },
    faqState: { openFaqIndex, toggleFaq }
  } = useGetInTouch();
  return (
    <section ref={sectionRef} id="contact" className="py-16 px-8 lg:px-16 relative">
      <div className="max-w-6xl mx-auto">

        {/* FAQ Section */}
        <div className="mb-20">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 font-orbitron">
              Frequently Asked <span className="text-[#302cff]">Questions</span>
            </h2>
            <p className="text-lg text-white/70 font-sora max-w-3xl mx-auto">
              Get answers to common questions about Trego Tech and our services
            </p>
          </div>

          <div className="max-w-4xl mx-auto space-y-4">
            {FAQ_DATA.map((faq, index) => (
              <div
                key={index}
                className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden"
              >
                <button
                  onClick={() => toggleFaq(index)}
                  className="w-full px-6 py-5 text-left flex items-center justify-between group"
                >
                  <h3 className="text-lg font-semibold text-white font-sora">
                    {faq.question}
                  </h3>
                  <div className={`text-purple-400 text-2xl transition-transform duration-300 ${openFaqIndex === index ? 'rotate-45' : ''}`}>
                    +
                  </div>
                </button>

                <div className={`overflow-hidden transition-all duration-500 ease-in-out ${
                  openFaqIndex === index ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                }`}>
                  <div className="px-6 pb-5">
                    <div className="border-t border-white/10 pt-4">
                      <p className="text-white/80 font-sora leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Contact Section */}
        <div className="text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-8 font-orbitron">
            {SECTION_CONTENT.title}
          </h2>
          <p className="text-lg text-white/70 mb-12 font-sora">
            {SECTION_CONTENT.description}
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {CONTACT_METHODS.map((method, index) => (
              <div key={index} className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6" data-cursor="pointer">
                <div className="text-4xl mb-4">{method.icon}</div>
                <h3 className="text-xl font-semibold text-white mb-3 font-sora">{method.title}</h3>
                <p className="text-white/70 font-sora">{method.value}</p>
              </div>
            ))}
          </div>
        </div>

      </div>
    </section>
  );
}
