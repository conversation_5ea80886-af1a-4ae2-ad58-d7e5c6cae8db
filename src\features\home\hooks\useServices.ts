"use client";

import { useEffect, useRef, useCallback } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

export const useServices = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLDivElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const gridRef = useRef<HTMLDivElement>(null);
  const featuredRef = useRef<HTMLDivElement>(null);
  const completeSolutionsTitleRef = useRef<HTMLDivElement>(null);
  const completeSolutionsDescRef = useRef<HTMLParagraphElement>(null);
  const completeSolutionsGridRef = useRef<HTMLDivElement>(null);

  // Performance optimization: Cache animation timelines and hover states
  const animationCache = useRef<{
    mainTimeline?: gsap.core.Timeline;
    scrollTriggers?: ScrollTrigger[];
    hoverTimelines?: Map<HTMLElement, gsap.core.Timeline>;
    isScrollComplete?: boolean;
  }>({
    hoverTimelines: new Map(),
    isScrollComplete: false
  });

  // Performance optimized: Simplified animation initialization
  const initializeAnimations = useCallback(() => {
    const section = sectionRef.current;
    if (!section) return;

    // Return cached timeline if it exists
    if (animationCache.current.mainTimeline) {
      return animationCache.current.mainTimeline;
    }

    // Batch all initial states for better performance - NO will-change on mount
    const allElements = [titleRef.current, subtitleRef.current, featuredRef.current].filter(Boolean);
    if (allElements.length > 0) {
      gsap.set(allElements, {
        opacity: 0,
        y: 30,
        scale: 0.95,
        force3D: true
      });
    }

    // Simplified grid items setup
    const gridItems = Array.from(gridRef.current?.children || []);
    if (gridItems.length > 0) {
      gsap.set(gridItems, {
        opacity: 0,
        y: 40,
        scale: 0.9,
        force3D: true
      });
    }

    // Performance optimized: Single ScrollTrigger with batched animations
    const scrollTrigger = ScrollTrigger.create({
      trigger: section,
      start: "top 100%", // More aggressive trigger to ensure it fires
      onEnter: () => {
        const tl = gsap.timeline({
          onStart: () => {
            // Only set will-change during animation
            [...allElements, ...gridItems.map(el => el as HTMLElement)].forEach(el => {
              if (el) el.style.willChange = 'transform, opacity';
            });
          },
          onComplete: () => {
            // Clean up will-change immediately after animation
            [...allElements, ...gridItems.map(el => el as HTMLElement)].forEach(el => {
              if (el) el.style.willChange = 'auto';
            });

            // Mark scroll animation as complete and enable hover effects
            animationCache.current.isScrollComplete = true;

            // Defer hover animations until scroll is done (Fix #5)
            requestIdleCallback(() => {
              addOptimizedHoverEffects();
            }, { timeout: 1000 });
          }
        });

        // Single batched animation for all main elements
        tl.to(allElements, {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.8,
          ease: "power2.out",
          force3D: true
        });

        // Simplified grid animation - no stagger for better performance
        if (gridItems.length > 0) {
          tl.to(gridItems, {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: 1,
            ease: "power2.out",
            force3D: true
          }, "-=0.4");
        }

        // Cache the timeline
        animationCache.current.mainTimeline = tl;
      }
    });

    // AMAZING HOVER EFFECTS: Restored with Performance Optimizations
    const addOptimizedHoverEffects = () => {
      // Only add hover effects if scroll animation is complete
      if (!animationCache.current.isScrollComplete) return;

      const cards = Array.from(gridRef.current?.children || []);

      cards.forEach((card) => {
        const cardElement = card as HTMLElement;

        // Cache selectors for performance
        const icon = cardElement.querySelector('.service-icon');
        const cardContainer = cardElement.querySelector('.service-card-container');

        if (!cardContainer || !icon) return;

        // PERFORMANCE: Simple state management
        let isHovering = false;
        let isAnimating = false;

        const handleMouseEnter = () => {
          if (isHovering || isAnimating) return;
          isHovering = true;
          isAnimating = true;

          // Kill any existing timeline
          const existingTimeline = animationCache.current.hoverTimelines?.get(cardElement);
          if (existingTimeline) {
            existingTimeline.kill();
          }

          // BEAUTIFUL & OPTIMIZED: Smooth sequential animation
          const tl = gsap.timeline({
            defaults: { ease: "power2.out" },
            onComplete: () => {
              isAnimating = false;
            }
          });

          // Step 1: Elegant logo fade out with slight scale
          tl.to(icon, {
            opacity: 0,
            scale: 0.9,
            duration: 0.2
          }, 0)

          // Step 2: Beautiful card lift with subtle scale
          .to(cardContainer, {
            scale: 1.05,
            y: -8,
            duration: 0.3
          }, 0.1)

          // Step 3: Graceful logo return smaller with rotation
          .to(icon, {
            opacity: 1,
            scale: 0.7,
            rotation: 15,
            duration: 0.25
          }, 0.3);

          animationCache.current.hoverTimelines?.set(cardElement, tl);
        };

        const handleMouseLeave = () => {
          if (!isHovering || isAnimating) return;
          isHovering = false;
          isAnimating = true;

          // Kill existing timeline
          const existingTimeline = animationCache.current.hoverTimelines?.get(cardElement);
          if (existingTimeline) {
            existingTimeline.kill();
          }

          // CREATIVE RESET: Smooth return with reverse rotation
          const tl = gsap.timeline({
            defaults: { ease: "power2.out" },
            onComplete: () => {
              isAnimating = false;
            }
          });

          // Step 1: Logo reverse spin back to normal
          tl.to(icon, {
            scale: 1,
            rotation: 0,
            duration: 0.5,
            ease: "back.out(1.2)",
            transformOrigin: "center center"
          }, 0)

          // Step 2: Card gentle settle back
          .to(cardContainer, {
            scale: 1,
            y: 0,
            duration: 0.4,
            ease: "power2.out"
          }, 0.1);

          animationCache.current.hoverTimelines?.set(cardElement, tl);
        };

        // ULTRA SIMPLE: Direct event listeners with state checks
        // Use passive event listeners for better performance
        cardElement.addEventListener('mouseenter', handleMouseEnter, { passive: true });
        cardElement.addEventListener('mouseleave', handleMouseLeave, { passive: true });
      });
    };

    // Store scroll trigger for cleanup
    animationCache.current.scrollTriggers = [scrollTrigger];

    return scrollTrigger;
  }, []);

  useEffect(() => {
    initializeAnimations();

    return () => {
      // Clean up all cached animations
      if (animationCache.current.mainTimeline) {
        animationCache.current.mainTimeline.kill();
      }
      if (animationCache.current.scrollTriggers) {
        animationCache.current.scrollTriggers.forEach(trigger => trigger.kill());
      }
      animationCache.current = {};
    };
  }, [initializeAnimations]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('services-animate-in');

            if (entry.target === gridRef.current) {
              const cards = entry.target.querySelectorAll('.service-card');
              cards.forEach((card, index) => {
                setTimeout(() => {
                  card.classList.add('services-animate-in');
                }, index * 100);
              });
            }
          }
        });
      },
      { threshold: 0.1, rootMargin: '200px 0px' }
    );

    const elements = [titleRef.current, subtitleRef.current, gridRef.current, completeSolutionsTitleRef.current, completeSolutionsDescRef.current, completeSolutionsGridRef.current];
    elements.forEach((el) => {
      if (el) observer.observe(el);
    });

    return () => {
      observer.disconnect();
    };
  }, []);

  return {
    refs: {
      sectionRef,
      titleRef,
      subtitleRef,
      gridRef,
      featuredRef,
      completeSolutionsTitleRef,
      completeSolutionsDescRef,
      completeSolutionsGridRef
    }
  };
};
