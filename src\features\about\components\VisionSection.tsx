interface VisionSectionProps {
  visionRef: React.RefObject<HTMLDivElement | null>;
}

export default function VisionSection({ visionRef }: VisionSectionProps) {
  return (
    <div ref={visionRef} className="space-y-8">
      <h2 className="text-4xl md:text-5xl font-bold text-white font-orbitron">
        Our Vision
      </h2>
      <div className="space-y-6">
        <p className="text-lg text-gray-300 font-sora leading-relaxed">
          To be a leading technology partner, build innovative digital solutions across industries, and enhance how businesses and users interact with technology.
        </p>
      </div>
    </div>
  );
}
