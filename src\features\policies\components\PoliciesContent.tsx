interface PolicyData {
  id: string;
  title: string;
  content: string;
  lastUpdated: string;
}

interface PoliciesContentProps {
  contentRef: React.RefObject<HTMLDivElement>;
  policies: PolicyData[];
  loading: boolean;
  error: string | null;
}

export default function PoliciesContent({ contentRef, policies, loading, error }: PoliciesContentProps) {
  if (loading) {
    return (
      <div ref={contentRef} className="text-center py-20">
        <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-[#302cff]"></div>
        <p className="text-gray-300 mt-4 font-sora">Loading policies...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div ref={contentRef} className="text-center py-20">
        <div className="bg-red-500/10 border border-red-500/30 rounded-2xl p-8 max-w-2xl mx-auto">
          <h3 className="text-xl font-bold text-red-400 mb-4 font-orbitron">Error Loading Policies</h3>
          <p className="text-gray-300 font-sora">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div ref={contentRef} className="space-y-8">
      {!Array.isArray(policies) || policies.length === 0 ? (
        <div className="text-center py-20">
          <p className="text-gray-300 text-lg font-sora">No policies available at the moment.</p>
        </div>
      ) : (
        policies.map((policy) => (
          <div key={policy.id} className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-xl rounded-3xl p-8 md:p-12 border border-gray-700/30 shadow-2xl">
            <div className="mb-6">
              <h2 className="text-2xl md:text-3xl font-bold text-white mb-3 font-orbitron">
                {policy.title}
              </h2>
              <p className="text-sm text-gray-400 font-sora">
                Last updated: {new Date(policy.lastUpdated).toLocaleDateString()}
              </p>
            </div>
            
            <div className="prose prose-invert max-w-none">
              <div 
                className="text-gray-300 font-sora leading-relaxed"
                dangerouslySetInnerHTML={{ __html: policy.content }}
              />
            </div>
          </div>
        ))
      )}
    </div>
  );
}
