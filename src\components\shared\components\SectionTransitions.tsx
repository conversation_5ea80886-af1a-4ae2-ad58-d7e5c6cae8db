"use client";

import { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

interface SectionTransitionsProps {
  sections: string[]; // Array of section IDs
}

export default function SectionTransitions({ sections }: SectionTransitionsProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Create transition effects for each section
    sections.forEach((sectionId, index) => {
      const section = document.getElementById(sectionId);
      if (!section) return;

      // Create section-specific background overlay
      const overlay = document.createElement("div");
      overlay.className = `section-overlay section-${index}`;
      overlay.style.cssText = `
        position: fixed;
        inset: 0;
        pointer-events: none;
        z-index: 1;
        opacity: 0;
      `;
      container.appendChild(overlay);

      // Different background for each section
      const backgrounds = [
        // Landing - Dark Blue/Blue gradient with particles
        "radial-gradient(circle at 20% 80%, rgba(48, 44, 255, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(0, 225, 255, 0.3) 0%, transparent 50%)",
        
        // Services - Green/Cyan tech grid
        "linear-gradient(45deg, rgba(0, 255, 127, 0.1) 0%, rgba(0, 225, 255, 0.2) 100%), repeating-linear-gradient(90deg, transparent, transparent 98px, rgba(0, 255, 127, 0.1) 100px)",
        
        // About - Orange/Red energy waves
        "radial-gradient(ellipse at top, rgba(255, 69, 0, 0.2) 0%, transparent 70%), radial-gradient(ellipse at bottom, rgba(255, 165, 0, 0.15) 0%, transparent 70%)",
        
        // Contact - Deep purple/pink
        "linear-gradient(135deg, rgba(147, 51, 234, 0.2) 0%, rgba(219, 39, 119, 0.2) 100%)",
      ];

      overlay.style.background = backgrounds[index % backgrounds.length];

      // Scroll-triggered section transitions
      ScrollTrigger.create({
        trigger: section,
        start: "top 95%",
        end: "bottom 5%",
        scrub: 1,
        onUpdate: (self) => {
          const progress = self.progress;
          gsap.set(overlay, {
            opacity: progress,
          });
        },
        onEnter: () => {
          // Create section entrance effects
          createSectionEffects(overlay, index);
        },
      });

      // Parallax effect for section content
      ScrollTrigger.create({
        trigger: section,
        start: "top bottom",
        end: "bottom top",
        scrub: 1,
        onUpdate: (self) => {
          const progress = self.progress;
          const elements = section.querySelectorAll('[data-parallax]');
          elements.forEach((element) => {
            const speed = parseFloat((element as HTMLElement).dataset.parallax || "1");
            gsap.set(element, {
              y: progress * 100 * speed,
            });
          });
        },
      });
    });

    // Create section-specific effects
    const createSectionEffects = (overlay: HTMLElement, sectionIndex: number) => {
      switch (sectionIndex) {
        case 0: // Landing
          createFloatingOrbs(overlay);
          break;
        case 1: // Services
          createTechGrid(overlay);
          break;
        case 2: // About
          createEnergyWaves(overlay);
          break;
        case 3: // Contact
          createParticleField(overlay);
          break;
      }
    };

    const createFloatingOrbs = (container: HTMLElement) => {
      // Reduced from 10 to 4 orbs for better performance
      for (let i = 0; i < 4; i++) {
        const orb = document.createElement("div");
        orb.style.cssText = `
          position: absolute;
          width: ${Math.random() * 15 + 8}px;
          height: ${Math.random() * 15 + 8}px;
          background: radial-gradient(circle, ${Math.random() > 0.5 ? '#BA42FF' : '#00E1FF'} 0%, transparent 70%);
          border-radius: 50%;
          left: ${Math.random() * 100}%;
          top: ${Math.random() * 100}%;
          opacity: 0.4;
          will-change: transform;
        `;
        container.appendChild(orb);

        // Slower, less intensive animation
        gsap.to(orb, {
          y: `random(-50, 50)`,
          x: `random(-50, 50)`,
          duration: `random(8, 15)`,
          repeat: -1,
          yoyo: true,
          ease: "sine.inOut",
        });
      }
    };

    const createTechGrid = (container: HTMLElement) => {
      const grid = document.createElement("div");
      grid.style.cssText = `
        position: absolute;
        inset: 0;
        background-image:
          linear-gradient(rgba(0, 255, 127, 0.05) 1px, transparent 1px),
          linear-gradient(90deg, rgba(0, 255, 127, 0.05) 1px, transparent 1px);
        background-size: 60px 60px;
        opacity: 0.2;
      `;
      container.appendChild(grid);

      // Removed animation for better performance - static grid only
    };

    const createEnergyWaves = (container: HTMLElement) => {
      // Reduced from 5 to 2 waves for better performance
      for (let i = 0; i < 2; i++) {
        const wave = document.createElement("div");
        wave.style.cssText = `
          position: absolute;
          width: 150%;
          height: 150%;
          left: -25%;
          top: -25%;
          background: radial-gradient(circle, transparent 40%, rgba(255, 69, 0, 0.06) 60%, transparent 80%);
          border-radius: 50%;
          will-change: transform;
        `;
        container.appendChild(wave);

        // Slower rotation for better performance
        gsap.to(wave, {
          rotation: 360,
          duration: `random(20, 30)`,
          repeat: -1,
          ease: "none",
        });
      }
    };

    const createParticleField = (container: HTMLElement) => {
      // Reduced from 50 to 15 particles for much better performance
      for (let i = 0; i < 15; i++) {
        const particle = document.createElement("div");
        particle.style.cssText = `
          position: absolute;
          width: 1.5px;
          height: 1.5px;
          background: ${Math.random() > 0.5 ? '#302cff' : '#DB2777'};
          left: ${Math.random() * 100}%;
          top: ${Math.random() * 100}%;
          will-change: transform;
        `;
        container.appendChild(particle);

        // Slower, less intensive animation
        gsap.to(particle, {
          y: `random(-100, 100)`,
          x: `random(-100, 100)`,
          opacity: `random(0.3, 0.8)`,
          duration: `random(6, 12)`,
          repeat: -1,
          yoyo: true,
          ease: "sine.inOut",
        });
      }
    };

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, [sections]);

  return <div ref={containerRef} className="fixed inset-0 pointer-events-none z-0" />;
}
