"use client";

import { useEffect } from "react";
import Image from "next/image";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { ANIMATION_CONFIG, getTriggerPoints, debugLog } from "@/config/animationConfig";
import { secondaryServices } from "../consts/servicesConsts";
import { useServices } from "../hooks/useServices";

gsap.registerPlugin(ScrollTrigger);

export default function Services() {
  const {
    refs: {
      sectionRef,
      titleRef,
      subtitleRef,
      gridRef,
      completeSolutionsTitleRef,
      completeSolutionsDescRef,
      completeSolutionsGridRef
    }
  } = useServices();

  useEffect(() => {
    const checkLoadingComplete = () => {
      const loadingScreen = document.querySelector('[data-loading-screen]');
      if (!loadingScreen) {
        initScrollAnimations();
      } else {
        setTimeout(checkLoadingComplete, 100);
      }
    };

    const initScrollAnimations = () => {
      debugLog("Initializing Services animations");

      gsap.set(".service-image", ANIMATION_CONFIG.INITIAL_STATES.IMAGES);
      gsap.set(".service-title", ANIMATION_CONFIG.INITIAL_STATES.TEXT);
      gsap.set(".service-description", ANIMATION_CONFIG.INITIAL_STATES.TEXT);
      gsap.set(".service-features", ANIMATION_CONFIG.INITIAL_STATES.CARDS);
      gsap.set(".service-button", ANIMATION_CONFIG.INITIAL_STATES.BUTTONS);
      gsap.set(".complete-solutions-title", ANIMATION_CONFIG.INITIAL_STATES.TEXT);
      gsap.set(".complete-solutions-description", ANIMATION_CONFIG.INITIAL_STATES.TEXT);
      gsap.set(".complete-solutions-card", ANIMATION_CONFIG.INITIAL_STATES.CARDS);

    // Create entrance animations for each service
    const services = [
      {
        imageSelector: ".service-image-0",
        textSelector: ".service-text-0",
        titleSelector: ".service-title-0",
        descSelector: ".service-description-0",
        featuresSelector: ".service-features-0",
        buttonSelector: ".service-button-0",
        delay: 0
      },
      {
        imageSelector: ".service-image-1",
        textSelector: ".service-text-1",
        titleSelector: ".service-title-1",
        descSelector: ".service-description-1",
        featuresSelector: ".service-features-1",
        buttonSelector: ".service-button-1",
        delay: 0.2
      },
      {
        imageSelector: ".service-image-2",
        textSelector: ".service-text-2",
        titleSelector: ".service-title-2",
        descSelector: ".service-description-2",
        featuresSelector: ".service-features-2",
        buttonSelector: ".service-button-2",
        delay: 0.4
      },
    ];

    services.forEach(({ imageSelector, titleSelector, descSelector, featuresSelector, buttonSelector }) => {
      const imageTrigger = getTriggerPoints('SERVICES', 'IMAGES');
      const textTrigger = getTriggerPoints('SERVICES', 'TEXT');
      const featuresTrigger = getTriggerPoints('SERVICES', 'FEATURES');
      const buttonTrigger = getTriggerPoints('SERVICES', 'BUTTONS');

      // Scroll-responsive image animation using config
      ScrollTrigger.create({
        trigger: imageSelector,
        start: imageTrigger.start,
        end: imageTrigger.end,
        scrub: ANIMATION_CONFIG.SCRUB.SERVICES_IMAGES,
        animation: gsap.to(imageSelector, {
          opacity: 1,
          scale: 1,
          rotationY: 0,
          y: 0,
          duration: ANIMATION_CONFIG.DURATIONS.SLOW,
          ease: ANIMATION_CONFIG.EASING.SMOOTH,
          transformOrigin: "center center",
        }),
      });

      // Scroll-responsive text animations using config
      ScrollTrigger.create({
        trigger: titleSelector,
        start: textTrigger.start,
        end: textTrigger.end,
        scrub: ANIMATION_CONFIG.SCRUB.SERVICES_TEXT,
        animation: gsap.to(titleSelector, {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: ANIMATION_CONFIG.DURATIONS.NORMAL,
          ease: ANIMATION_CONFIG.EASING.SMOOTH,
        }),
      });

      ScrollTrigger.create({
        trigger: descSelector,
        start: textTrigger.start,
        end: textTrigger.end,
        scrub: ANIMATION_CONFIG.SCRUB.SERVICES_TEXT,
        animation: gsap.to(descSelector, {
          opacity: 1,
          y: 0,
          duration: ANIMATION_CONFIG.DURATIONS.NORMAL,
          ease: ANIMATION_CONFIG.EASING.SMOOTH,
        }),
      });

      ScrollTrigger.create({
        trigger: featuresSelector,
        start: featuresTrigger.start,
        end: featuresTrigger.end,
        scrub: ANIMATION_CONFIG.SCRUB.SERVICES_FEATURES,
        animation: gsap.to(featuresSelector, {
          opacity: 1,
          y: 0,
          duration: ANIMATION_CONFIG.DURATIONS.NORMAL,
          ease: ANIMATION_CONFIG.EASING.SMOOTH,
        }),
      });

      ScrollTrigger.create({
        trigger: buttonSelector,
        start: buttonTrigger.start,
        end: buttonTrigger.end,
        scrub: ANIMATION_CONFIG.SCRUB.SERVICES_BUTTONS,
        animation: gsap.to(buttonSelector, {
          opacity: 1,
          scale: 1,
          y: 0,
          duration: ANIMATION_CONFIG.DURATIONS.FAST,
          ease: ANIMATION_CONFIG.EASING.SMOOTH,
        }),
      });
    });

    // Complete Solutions Section Animations
    const completeSolutionsTrigger = getTriggerPoints('COMPLETE_SOLUTIONS', 'TITLE');
    const completeSolutionsDescTrigger = getTriggerPoints('COMPLETE_SOLUTIONS', 'DESCRIPTION');
    const completeSolutionsCardsTrigger = getTriggerPoints('COMPLETE_SOLUTIONS', 'CARDS');

    // Complete Solutions Title Animation
    ScrollTrigger.create({
      trigger: ".complete-solutions-title",
      start: completeSolutionsTrigger.start,
      end: completeSolutionsTrigger.end,
      scrub: ANIMATION_CONFIG.SCRUB.COMPLETE_SOLUTIONS_TITLE,
      animation: gsap.to(".complete-solutions-title", {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: ANIMATION_CONFIG.DURATIONS.NORMAL,
        ease: ANIMATION_CONFIG.EASING.SMOOTH,
      }),
    });

    // Complete Solutions Description Animation
    ScrollTrigger.create({
      trigger: ".complete-solutions-description",
      start: completeSolutionsDescTrigger.start,
      end: completeSolutionsDescTrigger.end,
      scrub: ANIMATION_CONFIG.SCRUB.COMPLETE_SOLUTIONS_DESCRIPTION,
      animation: gsap.to(".complete-solutions-description", {
        opacity: 1,
        y: 0,
        duration: ANIMATION_CONFIG.DURATIONS.NORMAL,
        ease: ANIMATION_CONFIG.EASING.SMOOTH,
      }),
    });

    // Complete Solutions Cards Animation (staggered)
    ScrollTrigger.create({
      trigger: ".complete-solutions-grid",
      start: completeSolutionsCardsTrigger.start,
      end: completeSolutionsCardsTrigger.end,
      scrub: ANIMATION_CONFIG.SCRUB.COMPLETE_SOLUTIONS_CARDS,
      animation: gsap.to(".complete-solutions-card", {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: ANIMATION_CONFIG.DURATIONS.NORMAL,
        ease: ANIMATION_CONFIG.EASING.SMOOTH,
        stagger: ANIMATION_CONFIG.STAGGER.FEATURE_CARDS,
      }),
    });

    };

    checkLoadingComplete();

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);







  return (
    <section
      ref={sectionRef}
      id="services"
      className="min-h-screen py-20 px-4 lg:px-8 relative z-10"
      style={{
        willChange: 'auto',
        backfaceVisibility: 'hidden',
        transform: 'translateZ(0)',
        contain: 'layout style paint',
      }}
    >
      {/* Subtle CSS Fallback - Only if GSAP fails */}
      <style jsx>{`
        .service-image,
        .service-title,
        .service-description,
        .service-features,
        .service-button {
          opacity: 1;
          transform: translateY(0) scale(1) rotateY(0);
        }
      `}</style>

      {/* Modern Header */}
      <div className="text-center mb-20">
        <div ref={titleRef} className="services-element">
          <h2 className="text-5xl md:text-6xl lg:text-7xl font-black text-white font-orbitron leading-tight mb-6">
            What We <span className="bg-gradient-to-r from-[#4d42ff] via-[#302cff] to-[#1e1a99] bg-clip-text text-transparent">Create</span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-[#302cff] to-[#1e1a99] mx-auto rounded-full"></div>
        </div>

        <p ref={subtitleRef} className="services-element text-xl md:text-2xl text-gray-200 max-w-4xl mx-auto font-sora leading-relaxed mt-8">
          Transforming ideas into digital reality with cutting-edge technology solutions
        </p>
      </div>

      {/* Main Featured Services */}
      <div ref={gridRef} className="max-w-7xl mx-auto services-element mb-32">

        {/* Clean Alternating Layout */}
        <div className="space-y-32">
          {/* Mobile Development - Animation Left, Text Right */}
          <div className="flex flex-col lg:flex-row items-center gap-16">
            {/* Mobile Development Image with Cool Entrance Animation */}
            <div className="flex-shrink-0 w-80 h-80 flex items-center justify-center relative">
              <div className="service-image service-image-0 w-full h-full flex items-center justify-center relative">
                {/* Mobile Development Image */}
                <Image
                  src="/assets/services/images/mobile.png"
                  alt="Mobile Development"
                  fill
                  className="w-full h-full object-contain opacity-90 transform hover:scale-110 transition-transform duration-300"
                />

                {/* Subtle glow effect behind the image */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-[#302cff]/20 opacity-30 blur-3xl rounded-full scale-110 -z-10"></div>
              </div>
            </div>

            {/* Text Content with Cool Animations */}
            <div className="service-text service-text-0 flex-1 text-center lg:text-left">
              <h3 className="service-title service-title-0 text-4xl lg:text-6xl font-bold text-white font-orbitron mb-6 bg-gradient-to-r from-blue-400 to-[#302cff] bg-clip-text text-transparent">
                Mobile Development
              </h3>
              <p className="service-description service-description-0 text-xl text-gray-300 font-sora leading-relaxed mb-8">
                Native and cross-platform mobile applications with cutting-edge UI/UX design and seamless performance across iOS and Android.
              </p>

            </div>
          </div>

          {/* Web Development - Text Left, Animation Right */}
          <div className="flex flex-col lg:flex-row-reverse items-center gap-16">
            {/* Web Development Image with Cool Entrance Animation */}
            <div className="flex-shrink-0 w-80 h-80 flex items-center justify-center relative">
              <div className="service-image service-image-1 w-full h-full flex items-center justify-center relative">
                {/* Web Development Image */}
                <Image
                  src="/assets/services/images/web.png"
                  alt="Web Development"
                  fill
                  className="w-full h-full object-contain opacity-90 transform hover:scale-110 transition-transform duration-300"
                />

                {/* Subtle glow effect behind the image */}
                <div className="absolute inset-0 bg-gradient-to-br from-[#302cff]/20 to-pink-600/20 opacity-30 blur-3xl rounded-full scale-110 -z-10"></div>
              </div>
            </div>

            {/* Text Content with Cool Animations */}
            <div className="service-text service-text-1 flex-1 text-center lg:text-left">
              <h3 className="service-title service-title-1 text-4xl lg:text-6xl font-bold text-white font-orbitron mb-6 bg-gradient-to-r from-[#302cff] to-pink-600 bg-clip-text text-transparent">
                Web Development
              </h3>
              <p className="service-description service-description-1 text-xl text-gray-300 font-sora leading-relaxed mb-8">
                Modern, responsive websites and web applications with exceptional user experience, performance, and SEO optimization.
              </p>

            </div>
          </div>

          {/* AI Automation - Animation Left, Text Right */}
          <div className="flex flex-col lg:flex-row items-center gap-16">
            {/* AI Automation Image with Cool Entrance Animation */}
            <div className="flex-shrink-0 w-80 h-80 flex items-center justify-center relative">
              <div className="service-image service-image-2 w-full h-full flex items-center justify-center relative">
                {/* AI Automation Image */}
                <Image
                  src="/assets/services/images/Ai.png"
                  alt="AI Automation"
                  fill
                  className="w-full h-full object-contain opacity-90 transform hover:scale-110 transition-transform duration-300"
                />

                {/* Subtle glow effect behind the image */}
                <div className="absolute inset-0 bg-gradient-to-br from-green-400/20 to-[#302cff]/20 opacity-30 blur-3xl rounded-full scale-110 -z-10"></div>
              </div>
            </div>

            {/* Text Content with Cool Animations */}
            <div className="service-text service-text-2 flex-1 text-center lg:text-left">
              <h3 className="service-title service-title-2 text-4xl lg:text-6xl font-bold text-white font-orbitron mb-6 bg-gradient-to-r from-green-400 to-[#302cff] bg-clip-text text-transparent">
                AI Automation
              </h3>
              <p className="service-description service-description-2 text-xl text-gray-300 font-sora leading-relaxed mb-8">
                Intelligent automation solutions powered by AI to streamline workflows, enhance productivity, and drive business growth.
              </p>

            </div>
          </div>
        </div>
      </div>

      {/* Secondary Services */}
      <div className="max-w-7xl mx-auto mt-24">
        <div className="text-center mb-16">
          <h3
            ref={completeSolutionsTitleRef}
            className="complete-solutions-title text-3xl font-bold text-white font-orbitron mb-4"
          >
            Complete <span className="text-[#302cff]">Solutions</span>
          </h3>
          <p
            ref={completeSolutionsDescRef}
            className="complete-solutions-description text-gray-300 font-sora"
          >
            Everything you need for your digital transformation journey
          </p>
        </div>

        <div
          ref={completeSolutionsGridRef}
          className="complete-solutions-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
        >
          {secondaryServices.map((service, index) => (
            <div
              key={index}
              className="complete-solutions-card group relative"
            >
              <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-gray-900/60 to-gray-800/60 backdrop-blur-xl border border-[#302cff]/20 p-6 h-56 transition-all duration-300 group-hover:border-[#302cff]/40 group-hover:shadow-lg group-hover:shadow-[#302cff]/20 group-hover:-translate-y-1">
                {/* Simple Background Effect */}
                <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute inset-0 bg-gradient-to-br from-[#302cff]/10 to-[#1e1a99]/10"></div>
                </div>

                {/* Content */}
                <div className="relative z-10 h-full flex flex-col">
                  {/* Header */}
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-[#302cff] to-[#1e1a99] flex items-center justify-center text-lg transform group-hover:scale-105 transition-transform duration-300">
                      {service.icon}
                    </div>
                    <h4 className="text-lg font-bold text-white font-orbitron group-hover:text-[#4d42ff] transition-colors duration-300">
                      {service.title}
                    </h4>
                  </div>

                  {/* Description */}
                  <p className="text-gray-300 font-sora text-sm leading-relaxed mb-4 group-hover:text-gray-100 transition-colors duration-300">
                    {service.description}
                  </p>

                  {/* Features */}
                  <div className="space-y-1 mt-auto">
                    {service.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center gap-2">
                        <div className="w-1 h-1 rounded-full bg-[#302cff]"></div>
                        <span className="text-xs text-gray-400 font-sora">
                          {feature}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
