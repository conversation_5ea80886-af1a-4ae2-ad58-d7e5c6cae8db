"use client";

import { useState, useEffect } from "react";

export const usePageLoading = (splineReady: boolean = false) => {
  const [isLoading, setIsLoading] = useState(true);
  const [currentText, setCurrentText] = useState(0);

  // AI-themed loading texts
  const aiTexts = [
    "Initializing AI Systems...",
    "Loading Neural Networks...",
    "Calibrating Machine Learning Models...",
    "Optimizing Performance Algorithms...",
    "Preparing Intelligent Solutions...",
    "Activating Innovation Engine...",
    "Synchronizing Data Streams...",
    "Deploying Smart Technologies...",
  ];

  // Text rotation effect
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentText((prev) => (prev + 1) % aiTexts.length);
    }, 1500);

    return () => clearInterval(interval);
  }, [aiTexts.length]);

  // PERFORMANCE OPTIMIZED: Faster loading for better LCP
  useEffect(() => {
    // CRITICAL: Reduced loading time for better LCP
    const minLoadingTime = 800; // Reduced from 2000ms
    const maxLoadingTime = 3000; // Maximum wait time
    const startTime = Date.now();

    // Prioritize LCP over 3D model loading
    const checkLoadingComplete = () => {
      const elapsed = Date.now() - startTime;
      const minTimeReached = elapsed >= minLoadingTime;
      const maxTimeReached = elapsed >= maxLoadingTime;

      // Finish loading if:
      // 1. Minimum time passed AND (Spline ready OR max time reached)
      // 2. This prioritizes LCP over waiting for 3D model
      if (minTimeReached && (splineReady || maxTimeReached)) {
        setIsLoading(false);
      } else if (!maxTimeReached) {
        // Check again in 50ms for faster response
        setTimeout(checkLoadingComplete, 50);
      }
    };

    // Start checking after minimum time
    const loadingTimer = setTimeout(checkLoadingComplete, minLoadingTime);

    return () => {
      clearTimeout(loadingTimer);
    };
  }, [splineReady]);

  return {
    isLoading,
    currentText,
    aiTexts,
  };
};
