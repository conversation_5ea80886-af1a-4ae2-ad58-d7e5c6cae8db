"use client";

import { useEffect, useRef, useState, useCallback } from "react";
import { DotLottieReact } from '@lottiefiles/dotlottie-react';

interface DotLottieAnimationProps {
  src: string;
  className?: string;
  width?: number;
  height?: number;
  autoplay?: boolean;
  loop?: boolean;
  speed?: number;
  hover?: boolean;
  quality?: 'low' | 'medium' | 'high';
  preload?: boolean;
  onLoad?: () => void;
  onError?: (error: Error) => void;
}

export default function DotLottieAnimation({
  src,
  className = "",
  width = 200,
  height = 200,
  autoplay = true,
  loop = true,
  speed = 1,
  hover = false,

  onLoad,
  onError
}: DotLottieAnimationProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [hasError, setHasError] = useState(false);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Create fallback icons for when lottie fails to load
  const getFallbackIcon = useCallback(() => {
    if (src.includes('Mobile')) {
      return (
        <div className="relative w-full h-full flex items-center justify-center">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-600 rounded-xl flex items-center justify-center animate-pulse">
            <span className="text-2xl">📱</span>
          </div>
          <div className="absolute inset-0 border-2 border-blue-400/30 rounded-xl animate-ping"></div>
        </div>
      );
    } else if (src.includes('Web')) {
      return (
        <div className="relative w-full h-full flex items-center justify-center">
          <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-600 rounded-xl flex items-center justify-center animate-pulse">
            <span className="text-2xl">💻</span>
          </div>
          <div className="absolute inset-0 border-2 border-purple-400/30 rounded-xl animate-ping"></div>
        </div>
      );
    } else if (src.includes('Ai')) {
      return (
        <div className="relative w-full h-full flex items-center justify-center">
          <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-[#302cff] rounded-xl flex items-center justify-center animate-pulse">
            <span className="text-2xl">🤖</span>
          </div>
          <div className="absolute inset-0 border-2 border-green-400/30 rounded-xl animate-ping"></div>
        </div>
      );
    }

    // Default fallback
    return (
      <div className="relative w-full h-full flex items-center justify-center">
        <div className="w-12 h-12 bg-gradient-to-br from-[#302cff] to-[#1e1a99] rounded-xl flex items-center justify-center animate-pulse">
          <span className="text-2xl">⚡</span>
        </div>
        <div className="absolute inset-0 border-2 border-[#302cff]/30 rounded-xl animate-ping"></div>
      </div>
    );
  }, [src]);

  // Prevent hydration mismatch
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Intersection Observer for performance
  useEffect(() => {
    if (!containerRef.current || !isMounted) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(true);
          } else {
            setIsVisible(false);
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: '200px 0px' // Much larger root margin for earlier triggering
      }
    );

    observerRef.current.observe(containerRef.current);

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [isMounted]);

  // Hover controls
  const handleMouseEnter = useCallback(() => {
    if (hover) {
      setIsHovered(true);
    }
  }, [hover]);

  const handleMouseLeave = useCallback(() => {
    if (hover) {
      setIsHovered(false);
    }
  }, [hover]);

  // Handle loading and error states
  const handleLoad = useCallback(() => {
    setHasError(false);
    onLoad?.();
  }, [onLoad]);

  const handleError = useCallback((error: unknown) => {
    setHasError(true);
    onError?.(error instanceof Error ? error : new Error(String(error)));
  }, [onError]);

  // Prevent hydration mismatch
  if (!isMounted) {
    return (
      <div
        className={`dot-lottie-container ${className}`}
        style={{
          width: width,
          height: height,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          overflow: 'hidden'
        }}
      >
        <div className="flex items-center justify-center w-full h-full bg-gray-800/20 rounded-lg">
          <div className="w-8 h-8 border-2 border-purple-400/30 border-t-purple-400 rounded-full animate-spin"></div>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={`dot-lottie-container ${className}`}
      style={{
        width: width,
        height: height,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        overflow: 'hidden'
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {hasError ? (
        // Show fallback icon if lottie fails to load
        getFallbackIcon()
      ) : (
        // Show actual DotLottie animation
        <DotLottieReact
          src={src}
          loop={loop}
          autoplay={autoplay && isVisible && (!hover || isHovered)}
          speed={speed}
          style={{
            width: '100%',
            height: '100%',
          }}
          onLoad={handleLoad}
          onError={handleError}
        />
      )}
    </div>
  );
}
