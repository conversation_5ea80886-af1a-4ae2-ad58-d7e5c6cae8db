"use client";

import { useRef, useEffect } from "react";
import { useScrollControlledLottie } from "./useScrollControlledLottie";
import { LOTTIE_CONFIG, ANIMATION_ELEMENTS, ANIMATION_CHECK_INTERVAL, ANIMATION_START_DELAY, VIDEO_PLAYBACK_RATE } from "../consts/landingConsts";

export const useLanding = () => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const titleRef = useRef<HTMLDivElement>(null);
  const subtitleRef = useRef<HTMLDivElement>(null);
  const descriptionRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<HTMLDivElement>(null);

  const { registerLottieInstance } = useScrollControlledLottie(LOTTIE_CONFIG);

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.playbackRate = VIDEO_PLAYBACK_RATE;
    }

    const checkLoadingComplete = () => {
      const loadingScreen = document.querySelector('[data-loading-screen]');
      if (!loadingScreen) {
        startAnimations();
      } else {
        setTimeout(checkLoadingComplete, ANIMATION_CHECK_INTERVAL);
      }
    };

    const startAnimations = () => {
      const refs = [titleRef, subtitleRef, descriptionRef, animationRef];

      refs.forEach((ref, index) => {
        if (ref.current) {
          setTimeout(() => {
            if (ref.current) {
              ref.current.classList.add('animate-entrance');
            }
          }, ANIMATION_ELEMENTS[index].delay);
        }
      });
    };

    const timer = setTimeout(checkLoadingComplete, ANIMATION_START_DELAY);
    return () => clearTimeout(timer);
  }, []);

  return {
    refs: {
      videoRef,
      titleRef,
      subtitleRef,
      descriptionRef,
      animationRef
    },
    registerLottieInstance
  };
};
