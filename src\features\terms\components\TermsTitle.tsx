interface TermsTitleProps {
  titleRef: React.RefObject<HTMLDivElement>;
}

export default function TermsTitle({ titleRef }: TermsTitleProps) {
  return (
    <div ref={titleRef} className="text-center mb-16">
      <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 font-orbitron leading-tight">
        Terms of <span className="text-[#302cff]">Service</span>
      </h1>
      <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto font-sora leading-relaxed">
        Please read these terms and conditions carefully before using our services
      </p>
      <p className="text-sm text-gray-400 mt-4 font-sora">
        Last updated: {new Date().toLocaleDateString()}
      </p>
    </div>
  );
}
