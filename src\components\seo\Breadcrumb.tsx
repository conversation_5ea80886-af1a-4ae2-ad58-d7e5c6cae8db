'use client';

import Link from 'next/link';
import { ChevronRight, Home } from 'lucide-react';
import StructuredData from './StructuredData';

interface BreadcrumbItem {
  name: string;
  url: string;
  current?: boolean;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
  showHome?: boolean;
  separator?: React.ReactNode;
}

export default function Breadcrumb({ 
  items, 
  className = '', 
  showHome = true,
  separator = <ChevronRight className="w-4 h-4 text-gray-400" />
}: BreadcrumbProps) {
  // Ensure home is always first if showHome is true
  const breadcrumbItems = showHome 
    ? [{ name: 'Home', url: '/' }, ...items.filter(item => item.url !== '/')]
    : items;

  return (
    <>
      {/* Structured Data for Breadcrumbs */}
      <StructuredData 
        type="breadcrumb" 
        data={{ breadcrumbs: breadcrumbItems }} 
      />
      
      {/* Breadcrumb Navigation */}
      <nav 
        aria-label="Breadcrumb" 
        className={`flex items-center space-x-2 text-sm ${className}`}
        role="navigation"
      >
        <ol className="flex items-center space-x-2" itemScope itemType="https://schema.org/BreadcrumbList">
          {breadcrumbItems.map((item, index) => (
            <li 
              key={item.url} 
              className="flex items-center"
              itemProp="itemListElement" 
              itemScope 
              itemType="https://schema.org/ListItem"
            >
              {/* Add separator before each item except the first */}
              {index > 0 && (
                <span className="mx-2" aria-hidden="true">
                  {separator}
                </span>
              )}
              
              {/* Breadcrumb Item */}
              {item.current || index === breadcrumbItems.length - 1 ? (
                <span 
                  className="text-gray-500 font-medium cursor-default"
                  aria-current="page"
                  itemProp="name"
                >
                  {index === 0 && showHome ? (
                    <span className="flex items-center">
                      <Home className="w-4 h-4 mr-1" />
                      {item.name}
                    </span>
                  ) : (
                    item.name
                  )}
                </span>
              ) : (
                <Link 
                  href={item.url}
                  className="text-purple-400 hover:text-purple-300 transition-colors duration-200 font-medium"
                  itemProp="item"
                >
                  <span itemProp="name">
                    {index === 0 && showHome ? (
                      <span className="flex items-center">
                        <Home className="w-4 h-4 mr-1" />
                        {item.name}
                      </span>
                    ) : (
                      item.name
                    )}
                  </span>
                </Link>
              )}
              
              {/* Hidden position for structured data */}
              <meta itemProp="position" content={String(index + 1)} />
            </li>
          ))}
        </ol>
      </nav>
    </>
  );
}
