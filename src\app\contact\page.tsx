import ContactMain from "@/features/contact/components/ContactMain";
import StructuredData from "@/components/seo/StructuredData";
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Contact Us - TregoTech | Get in Touch for Technology Solutions',
  description: 'Contact TregoTech for innovative mobile app development, web development, and AI automation solutions. Reach out to our expert team in Egypt. Email: <EMAIL> | Phone: +201112628619',
  keywords: 'contact TregoTech, mobile app development contact, web development Egypt, AI automation services, technology consultation, TregoTech phone, TregoTech email, Egyptian tech company contact',
  authors: [{ name: 'TregoTech' }],
  creator: 'TregoTech',
  publisher: 'TregoTech',
  openGraph: {
    title: 'Contact TregoTech - Expert Technology Solutions in Egypt',
    description: 'Ready to transform your business with cutting-edge technology? Contact TregoTech for mobile app development, web solutions, and AI automation services.',
    url: 'https://tregotech.com/contact',
    siteName: 'TregoTech',
    images: [
      {
        url: '/assets/BlueLogo.png',
        width: 1200,
        height: 630,
        alt: 'Contact TregoTech - Technology Solutions',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Contact TregoTech - Expert Technology Solutions in Egypt',
    description: 'Ready to transform your business with cutting-edge technology? Contact TregoTech for expert solutions.',
    images: ['/assets/BlueLogo.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: 'https://tregotech.com/contact',
    languages: {
      'en': 'https://tregotech.com/contact',
      'ar': 'https://tregotech.com/ar/contact',
    },
  },
};

export default function ContactPage() {
  return (
    <>
      <StructuredData type="contactPage" />
      <StructuredData
        type="breadcrumb"
        data={{
          breadcrumbs: [
            { name: "Home", url: "/" },
            { name: "Contact Us", url: "/contact" }
          ]
        }}
      />
      <ContactMain />
    </>
  );
}
