"use client";

import { useState, useEffect } from "react";
import { TermsData, TermsResponse } from "@/types/policy";

export const useTerms = () => {
  const [terms, setTerms] = useState<TermsData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTerms = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('https://prod.tregotech.com/tr/admin/terms/getVisibleTerms', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        // Add cache control for better performance
        cache: 'force-cache',
        next: { revalidate: 3600 } // Revalidate every hour
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch terms: ${response.status} ${response.statusText}`);
      }
      
      const data: TermsResponse = await response.json();
      
      if (!data.data || !Array.isArray(data.data)) {
        throw new Error('Invalid response format: expected data array');
      }
      
      // Sort by index to maintain proper order
      const sortedTerms = data.data
        .filter(term => term.status === 'visible') // Only show visible terms
        .sort((a, b) => a.index - b.index);
      
      setTerms(sortedTerms);
    } catch (err) {
      console.error('Error fetching terms:', err);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTerms();
  }, []);

  const refetch = () => {
    fetchTerms();
  };

  return {
    terms,
    loading,
    error,
    refetch
  };
};
