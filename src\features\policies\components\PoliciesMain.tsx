"use client";

import { usePolicies } from "../hooks/usePolicies";
import Link from "next/link";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import ErrorDisplay from "@/components/ui/ErrorDisplay";
import LanguageToggle from "@/components/ui/LanguageToggle";

export default function PoliciesMain() {
  const { policies, loading, error, refetch, language, setLanguage, getTitle, getSubtitle, getDescription } = usePolicies();

  if (loading) {
    return <LoadingSpinner message="Loading policies..." />;
  }

  if (error) {
    return <ErrorDisplay error={error} onRetry={refetch} title="Error Loading Policies" />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0a0a0a] via-[#1a1a2e] to-[#16213e] py-20 px-6 lg:px-16">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          {/* Back to Home Button */}
          <div className="mb-6">
            <Link
              href="/"
              className="inline-flex items-center text-purple-400 hover:text-purple-300 transition-colors duration-300 font-sora"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back to Home
            </Link>
          </div>

          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6 font-orbitron">
            Our <span className="text-purple-400">Policies</span>
          </h1>
          <p className="text-lg text-gray-300 font-sora mb-8">
            Please read our terms and conditions carefully
          </p>

          {/* Language Toggle */}
          <LanguageToggle language={language} onLanguageChange={setLanguage} />
        </div>

        {/* Policies Content */}
        <div className="space-y-8">
          {policies.map((policy) => {
            const title = getTitle(policy);
            const subtitle = getSubtitle(policy);
            const descriptions = getDescription(policy);

            return (
              <div
                key={policy.id}
                className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8 hover:border-purple-500/30 transition-all duration-300"
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              >
                {title && (
                  <h2 className="text-2xl md:text-3xl font-bold text-white mb-4 font-orbitron">
                    {title}
                  </h2>
                )}

                {subtitle && (
                  <h3 className="text-xl text-purple-300 mb-6 font-sora">
                    {subtitle}
                  </h3>
                )}

                {descriptions && descriptions.length > 0 && (
                  <div className="space-y-4">
                    {policy.type === 'bullet' ? (
                      <ul className="space-y-3 list-none">
                        {descriptions.map((desc, index) => (
                          <li
                            key={index}
                            className="text-gray-300 leading-relaxed font-sora text-base md:text-lg flex items-start"
                          >
                            <span className="text-purple-400 mr-3 mt-1 flex-shrink-0">•</span>
                            <span>{desc}</span>
                          </li>
                        ))}
                      </ul>
                    ) : (
                      descriptions.map((desc, index) => (
                        <p
                          key={index}
                          className="text-gray-300 leading-relaxed font-sora text-base md:text-lg"
                        >
                          {desc}
                        </p>
                      ))
                    )}
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Footer */}
        <div className="text-center mt-16 pt-8 border-t border-white/10">
          <p className="text-gray-400 font-sora">
            Last updated: {new Date().toLocaleDateString()}
          </p>
          <p className="text-gray-500 text-sm mt-2 font-sora">
            © 2024 Trego Tech. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  );
}
