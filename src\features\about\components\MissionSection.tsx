interface MissionSectionProps {
  missionRef: React.RefObject<HTMLDivElement | null>;
}

export default function MissionSection({ missionRef }: MissionSectionProps) {
  return (
    <div ref={missionRef} className="space-y-8">
      <h2 className="text-4xl md:text-5xl font-bold text-white font-orbitron">
        Our Mission
      </h2>
      <div className="space-y-6">
        <p className="text-lg text-gray-300 font-sora leading-relaxed">
          Our mission is to leverage the power of technology to create transformative software that meets today&apos;s needs while anticipating tomorrow&apos;s challenges. Whether it&apos;s through travel, or new and emerging industries, we aim to build adaptable, future-proof solutions.
        </p>
      </div>
    </div>
  );
}
