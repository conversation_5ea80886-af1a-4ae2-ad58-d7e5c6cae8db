interface AboutTitleProps {
  titleRef: React.RefObject<HTMLDivElement | null>;
}

export default function AboutTitle({ titleRef }: AboutTitleProps) {
  return (
    <div ref={titleRef} className="text-center mb-20">
      <h1 className="text-6xl md:text-8xl font-bold text-white mb-6 font-orbitron leading-tight">
        About <span className="text-[#302cff]">Us</span>
      </h1>
      <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto font-sora leading-relaxed">
        Pioneering the future of travel technology in Egypt and beyond
      </p>
    </div>
  );
}
