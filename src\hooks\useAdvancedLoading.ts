"use client";

import { useState, useEffect, useRef, useMemo } from "react";

interface LoadingItem {
  id: string;
  name: string;
  type: 'image' | 'font' | 'animation' | 'video' | 'script';
  url: string;
  loaded: boolean;
  error: boolean;
  loadTime?: number;
  size?: number;
}
interface LoadingProgress {
  total: number;
  loaded: number;
  percentage: number;
  currentItem?: string;
  items: LoadingItem[];
  totalLoadTime: number;
  averageLoadTime: number;
}

export const useAdvancedLoading = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [progress, setProgress] = useState<LoadingProgress>({
    total: 0,
    loaded: 0,
    percentage: 0,
    items: [],
    totalLoadTime: 0,
    averageLoadTime: 0,
  });

  console.log('useAdvancedLoading initialized, isLoading:', isLoading);
  const [currentText, setCurrentText] = useState(0);
  const startTimeRef = useRef<number>(Date.now());
  const itemStartTimes = useRef<Map<string, number>>(new Map());

  // AI-themed loading texts
  const aiTexts = [
    "Initializing AI Systems...",
    "Loading Neural Networks...",
    "Calibrating Machine Learning Models...",
    "Optimizing Performance Algorithms...",
    "Preparing Intelligent Solutions...",
    "Activating Innovation Engine...",
    "Synchronizing Data Streams...",
    "Deploying Smart Technologies...",
    "Loading Service Assets...",
    "Preparing Mobile Showcase...",
    "Initializing Partner Systems...",
    "Optimizing User Experience...",
  ];

  // Define all assets that need to be loaded
  const assetsToLoad: Omit<LoadingItem, 'loaded' | 'error' | 'loadTime'>[] = useMemo(() => [
    // Fonts
    { id: 'font-orbitron', name: 'Orbitron Font', type: 'font', url: 'https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap' },
    { id: 'font-sora', name: 'Sora Font', type: 'font', url: 'https://fonts.googleapis.com/css2?family=Sora:wght@300;400;500;600;700&display=swap' },

    // Landing Assets
    { id: 'landing-animation', name: 'Landing Animation', type: 'animation', url: '/assets/landing/NewAnimationLottie.json' },

    // Service Images
    { id: 'service-mobile', name: 'Mobile Service Image', type: 'image', url: '/assets/services/images/mobile.png' },
    { id: 'service-web', name: 'Web Service Image', type: 'image', url: '/assets/services/images/web.png' },
    { id: 'service-ai', name: 'AI Service Image', type: 'image', url: '/assets/services/images/ai.png' },

    // Trego App Screenshots (using actual files)
    { id: 'trego-app-main', name: 'Trego App Main', type: 'image', url: '/assets/trego_app/app.webp' },
    { id: 'trego-screen1', name: 'Trego Screen 1', type: 'image', url: '/assets/trego_app/IMG_0224.webp' },
    { id: 'trego-screen2', name: 'Trego Screen 2', type: 'image', url: '/assets/trego_app/IMG_0225.webp' },
    { id: 'trego-screen3', name: 'Trego Screen 3', type: 'image', url: '/assets/trego_app/IMG_0226.webp' },
    { id: 'trego-screen4', name: 'Trego Screen 4', type: 'image', url: '/assets/trego_app/IMG_0227.webp' },
    { id: 'trego-screen5', name: 'Trego Screen 5', type: 'image', url: '/assets/trego_app/IMG_0228.webp' },
    { id: 'trego-screen6', name: 'Trego Screen 6', type: 'image', url: '/assets/trego_app/IMG_0229.webp' },
    { id: 'trego-screen7', name: 'Trego Screen 7', type: 'image', url: '/assets/trego_app/IMG_0230.webp' },
    { id: 'trego-screen8', name: 'Trego Screen 8', type: 'image', url: '/assets/trego_app/IMG_0231.webp' },
    { id: 'app-store', name: 'App Store Badge', type: 'image', url: '/assets/trego_app/appStore.webp' },
    { id: 'google-play', name: 'Google Play Badge', type: 'image', url: '/assets/trego_app/googlePlay.webp' },
    { id: 'trego-logo', name: 'Trego Logo', type: 'image', url: '/assets/trego_app/logo.png' },

    // Partner Assets
    { id: 'partner-image', name: 'Partner Image', type: 'image', url: '/assets/become_partner/partner.png' },
    { id: 'partner-animation', name: 'Partner Animation', type: 'animation', url: '/assets/become_partner/handshake.json' },

    // Service Animations (for fallback)
    { id: 'service-mobile-anim', name: 'Mobile Service Animation', type: 'animation', url: '/assets/services/Mobile.json' },
    { id: 'service-web-anim', name: 'Web Service Animation', type: 'animation', url: '/assets/services/Web.json' },
    { id: 'service-ai-anim', name: 'AI Service Animation', type: 'animation', url: '/assets/services/Ai.json' },

    // Icons and other assets
    { id: 'favicon', name: 'Favicon', type: 'image', url: '/favicon.ico' },
  ], []);

  // Text rotation effect
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentText((prev) => (prev + 1) % aiTexts.length);
    }, 1500);

    return () => clearInterval(interval);
  }, [aiTexts.length]);

  // Load a single asset
  const loadAsset = (asset: Omit<LoadingItem, 'loaded' | 'error' | 'loadTime'>): Promise<LoadingItem> => {
    return new Promise((resolve) => {
      const startTime = Date.now();
      itemStartTimes.current.set(asset.id, startTime);

      if (asset.type === 'image') {
        const img = new Image();
        img.onload = () => {
          const loadTime = Date.now() - startTime;
          resolve({
            ...asset,
            loaded: true,
            error: false,
            loadTime,
            size: img.naturalWidth * img.naturalHeight,
          });
        };
        img.onerror = () => {
          const loadTime = Date.now() - startTime;
          resolve({
            ...asset,
            loaded: false,
            error: true,
            loadTime,
          });
        };
        img.src = asset.url;
      } else if (asset.type === 'font') {
        // Load font by creating a link element
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = asset.url;
        link.onload = () => {
          const loadTime = Date.now() - startTime;
          resolve({
            ...asset,
            loaded: true,
            error: false,
            loadTime,
          });
        };
        link.onerror = () => {
          const loadTime = Date.now() - startTime;
          resolve({
            ...asset,
            loaded: false,
            error: true,
            loadTime,
          });
        };
        document.head.appendChild(link);
      } else if (asset.type === 'animation') {
        // Load JSON animation
        fetch(asset.url)
          .then(response => response.json())
          .then(() => {
            const loadTime = Date.now() - startTime;
            resolve({
              ...asset,
              loaded: true,
              error: false,
              loadTime,
            });
          })
          .catch(() => {
            const loadTime = Date.now() - startTime;
            resolve({
              ...asset,
              loaded: false,
              error: true,
              loadTime,
            });
          });
      } else {
        // Default handling for other types
        const loadTime = Date.now() - startTime;
        resolve({
          ...asset,
          loaded: true,
          error: false,
          loadTime,
        });
      }
    });
  };

  // Main loading effect
  useEffect(() => {
    // Disable scrolling and ensure page starts at top
    const disableScrolling = () => {
      // Scroll to top immediately
      window.scrollTo(0, 0);

      // Disable scrolling
      document.body.style.overflow = 'hidden';
      document.documentElement.style.overflow = 'hidden';

      // Prevent scroll restoration
      if ('scrollRestoration' in history) {
        history.scrollRestoration = 'manual';
      }

      // Add event listeners to prevent scrolling
      const preventScroll = (e: Event) => {
        e.preventDefault();
        e.stopPropagation();
        return false;
      };

      const preventKeyScroll = (e: KeyboardEvent) => {
        const scrollKeys = ['ArrowUp', 'ArrowDown', 'PageUp', 'PageDown', 'Home', 'End', ' '];
        if (scrollKeys.includes(e.key)) {
          e.preventDefault();
          return false;
        }
      };

      // Prevent various scroll methods
      window.addEventListener('scroll', preventScroll, { passive: false });
      window.addEventListener('wheel', preventScroll, { passive: false });
      window.addEventListener('touchmove', preventScroll, { passive: false });
      window.addEventListener('keydown', preventKeyScroll, { passive: false });

      return () => {
        // Re-enable scrolling
        document.body.style.overflow = '';
        document.documentElement.style.overflow = '';

        // Remove event listeners
        window.removeEventListener('scroll', preventScroll);
        window.removeEventListener('wheel', preventScroll);
        window.removeEventListener('touchmove', preventScroll);
        window.removeEventListener('keydown', preventKeyScroll);

        // Restore scroll restoration
        if ('scrollRestoration' in history) {
          history.scrollRestoration = 'auto';
        }
      };
    };

    const cleanupScrollDisabling = disableScrolling();

    const loadAllAssets = async () => {
      const items: LoadingItem[] = assetsToLoad.map(asset => ({
        ...asset,
        loaded: false,
        error: false,
      }));

      setProgress(prev => ({
        ...prev,
        total: items.length,
        items,
      }));

      // Load assets with progress tracking
      for (let i = 0; i < assetsToLoad.length; i++) {
        const asset = assetsToLoad[i];
        
        setProgress(prev => ({
          ...prev,
          currentItem: asset.name,
        }));

        try {
          const loadedAsset = await loadAsset(asset);
          
          setProgress(prev => {
            const newItems = [...prev.items];
            newItems[i] = loadedAsset;
            const loadedCount = newItems.filter(item => item.loaded).length;
            const totalLoadTime = newItems.reduce((sum, item) => sum + (item.loadTime || 0), 0);
            
            return {
              ...prev,
              loaded: loadedCount,
              percentage: Math.round((loadedCount / prev.total) * 100),
              items: newItems,
              totalLoadTime,
              averageLoadTime: totalLoadTime / Math.max(loadedCount, 1),
            };
          });
        } catch (error) {
          console.warn(`Failed to load asset: ${asset.name}`, error);
        }
      }

      // Minimum loading time for smooth experience
      const minLoadingTime = 4000; // Increased to 4 seconds to see the animation
      const elapsed = Date.now() - startTimeRef.current;
      const remainingTime = Math.max(0, minLoadingTime - elapsed);

      console.log('Loading screen will be visible for:', remainingTime + elapsed, 'ms');

      setTimeout(() => {
        setIsLoading(false);
        // Re-enable scrolling when loading is complete
        cleanupScrollDisabling();
      }, remainingTime);
    };

    loadAllAssets();

    // Cleanup function
    return () => {
      cleanupScrollDisabling();
    };
  }, [assetsToLoad]);

  return {
    isLoading,
    progress,
    currentText,
    aiTexts,
  };
};
