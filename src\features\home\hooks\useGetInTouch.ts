"use client";

import { useRef, useState } from "react";

export const useGetInTouch = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const [openFaqIndex, setOpenFaqIndex] = useState<number | null>(null);

  const toggleFaq = (index: number) => {
    setOpenFaqIndex(openFaqIndex === index ? null : index);
  };

  return {
    refs: {
      sectionRef
    },
    faqState: {
      openFaqIndex,
      toggleFaq
    }
  };
};
