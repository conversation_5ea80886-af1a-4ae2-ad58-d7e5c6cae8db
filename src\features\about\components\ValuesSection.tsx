interface ValuesSectionProps {
  valuesRef: React.RefObject<HTMLDivElement | null>;
}

const values = [
  {
    title: "Innovation",
    description: "Constantly pushing boundaries to deliver cutting-edge solutions that shape the future.",
    color: "text-[#302cff]"
  },
  {
    title: "Excellence",
    description: "Committed to delivering the highest quality in every project and interaction.",
    color: "text-purple-400"
  },
  {
    title: "Adaptability",
    description: "Building flexible solutions that evolve with changing needs and technologies.",
    color: "text-[#302cff]"
  }
];

export default function ValuesSection({ valuesRef }: ValuesSectionProps) {
  return (
    <div ref={valuesRef} className="mt-20">
      <div className="text-center">
        <h2 className="text-3xl md:text-4xl font-bold text-white mb-8 font-orbitron">
          Our Values
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {values.map((value, index) => (
            <div key={index} className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700/30">
              <h3 className={`text-xl font-bold ${value.color} mb-3 font-orbitron`}>
                {value.title}
              </h3>
              <p className="text-gray-300 font-sora text-sm leading-relaxed">
                {value.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
