"use client";

import { useState, useEffect } from "react";
import { PolicyData, PolicyResponse } from "@/types/policy";

export const usePolicies = () => {
  const [policies, setPolicies] = useState<PolicyData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPolicies = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('https://prod.tregotech.com/tr/admin/ourPolicy/getVisiblePolicy', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        // Add cache control for better performance
        cache: 'force-cache',
        next: { revalidate: 3600 } // Revalidate every hour
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch policies: ${response.status} ${response.statusText}`);
      }
      
      const data: PolicyResponse = await response.json();
      
      if (!data.data || !Array.isArray(data.data)) {
        throw new Error('Invalid response format: expected data array');
      }
      
      // Sort by index to maintain proper order
      const sortedPolicies = data.data
        .filter(policy => policy.status === 'visible') // Only show visible policies
        .sort((a, b) => a.index - b.index);
      
      setPolicies(sortedPolicies);
    } catch (err) {
      console.error('Error fetching policies:', err);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPolicies();
  }, []);

  const refetch = () => {
    fetchPolicies();
  };

  return {
    policies,
    loading,
    error,
    refetch
  };
};
