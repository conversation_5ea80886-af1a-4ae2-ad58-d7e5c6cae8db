"use client";

import { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Register ScrollTrigger plugin
if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

export const useOptimizedScrollRotation = () => {
  const modelRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<ScrollTrigger | null>(null);

  useEffect(() => {
    const model = modelRef.current;
    if (!model) return;

    // Performance optimized: Simplified scroll rotation with throttling
    animationRef.current = ScrollTrigger.create({
      trigger: model,
      start: "top bottom",
      end: "bottom top",
      scrub: 2, // Slower scrub for better performance
      onUpdate: (self) => {
        const progress = self.progress;
        
        // Simplified rotation - only Y axis for better performance
        gsap.set(model, {
          rotationY: progress * 180, // Half rotation instead of full
          transformOrigin: "center center",
          force3D: true
        });
      },
    });

    // Simplified hover effect - reduced intensity
    const handleMouseEnter = () => {
      gsap.to(model, {
        scale: 1.05,
        duration: 0.3,
        ease: "power2.out",
        force3D: true
      });
    };

    const handleMouseLeave = () => {
      gsap.to(model, {
        scale: 1,
        duration: 0.3,
        ease: "power2.out",
        force3D: true
      });
    };

    // Use simpler mouse events for better performance
    model.addEventListener("mouseenter", handleMouseEnter);
    model.addEventListener("mouseleave", handleMouseLeave);

    return () => {
      if (animationRef.current) {
        animationRef.current.kill();
      }
      model.removeEventListener("mouseenter", handleMouseEnter);
      model.removeEventListener("mouseleave", handleMouseLeave);
    };
  }, []);

  return { modelRef };
};
