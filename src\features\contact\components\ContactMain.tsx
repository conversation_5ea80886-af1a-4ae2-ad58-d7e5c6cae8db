"use client";

import { useContact } from "../hooks/useContact";
import ContactTitle from "./ContactTitle";
import ContactForm from "./ContactForm";
import ContactInfo from "./ContactInfo";

export default function ContactMain() {
  const { 
    refs, 
    formData, 
    errors, 
    isSubmitting, 
    submitStatus, 
    handleInputChange, 
    handleSubmit 
  } = useContact();

  return (
    <div ref={refs.containerRef} className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 w-72 h-72 bg-[#302cff]/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-[#302cff]/5 rounded-full blur-3xl"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 pt-32 pb-16 px-8 lg:px-16">
        <div className="max-w-7xl mx-auto">
          
          {/* Contact Title */}
          <ContactTitle titleRef={refs.titleRef} />

          {/* Contact Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            
            {/* Contact Form */}
            <ContactForm 
              formRef={refs.formRef}
              formData={formData}
              errors={errors}
              isSubmitting={isSubmitting}
              submitStatus={submitStatus}
              handleInputChange={handleInputChange}
              handleSubmit={handleSubmit}
            />

            {/* Contact Information */}
            <ContactInfo infoRef={refs.infoRef} />

          </div>
        </div>
      </div>
    </div>
  );
}
