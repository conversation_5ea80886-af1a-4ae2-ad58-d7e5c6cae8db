export const mainServices = [
  {
    icon: "📱",
    title: "Mobile Development",
    description: "Native and cross-platform mobile applications with cutting-edge UI/UX design and seamless performance across iOS and Android.",
    features: ["iOS & Android", "React Native", "Flutter", "Native Development", "App Store Optimization"],
    color: "from-blue-400 to-[#302cff]",
    bgGradient: "from-blue-500/20 to-[#302cff]/20",
    borderColor: "border-blue-400/40",
    shadowColor: "shadow-blue-500/30"
  },
  {
    icon: "💻",
    title: "Web Development",
    description: "Modern, responsive websites and web applications with exceptional user experience, performance, and SEO optimization.",
    features: ["React/Next.js", "Responsive Design", "Performance Optimization", "SEO", "E-commerce"],
    color: "from-[#302cff] to-pink-600",
    bgGradient: "from-[#302cff]/20 to-pink-600/20",
    borderColor: "border-[#302cff]/40",
    shadowColor: "shadow-[#302cff]/30"
  },
  {
    icon: "🤖",
    title: "AI Automation",
    description: "Intelligent automation solutions powered by AI to streamline workflows, enhance productivity, and drive business growth.",
    features: ["Process Automation", "Machine Learning", "Chatbots", "Predictive Analytics", "AI Integration"],
    color: "from-green-400 to-[#302cff]",
    bgGradient: "from-green-500/20 to-[#302cff]/20",
    borderColor: "border-green-400/40",
    shadowColor: "shadow-green-500/30"
  },
  {
    icon: "☁️",
    title: "Cloud Solutions",
    description: "Scalable cloud infrastructure and deployment solutions for modern applications with high availability and performance.",
    features: ["AWS/Azure", "Docker", "Kubernetes", "CI/CD", "Serverless"],
    color: "from-cyan-400 to-blue-600",
    bgGradient: "from-cyan-500/20 to-blue-600/20",
    borderColor: "border-cyan-400/40",
    shadowColor: "shadow-cyan-500/30"
  },
  {
    icon: "📊",
    title: "Data Analytics",
    description: "Transform your data into actionable insights with advanced analytics, visualization, and business intelligence solutions.",
    features: ["Data Visualization", "Business Intelligence", "Real-time Analytics", "Data Mining", "Reporting"],
    color: "from-orange-400 to-red-600",
    bgGradient: "from-orange-500/20 to-red-600/20",
    borderColor: "border-orange-400/40",
    shadowColor: "shadow-orange-500/30"
  }
];

export const secondaryServices = [
  {
    icon: "🔒",
    title: "Security",
    description: "Enterprise-grade security solutions and data protection.",
    features: ["Data Encryption", "Security Audits", "Compliance"]
  },
  {
    icon: "⚡",
    title: "Speed Optimization",
    description: "Performance optimization for lightning-fast applications.",
    features: ["Performance Tuning", "Caching", "CDN Integration"]
  },
  {
    icon: "🔗",
    title: "External Integration",
    description: "Seamless third-party integrations and API development.",
    features: ["API Development", "Third-party APIs", "Microservices"]
  },
  {
    icon: "📊",
    title: "Data Analytics",
    description: "Advanced analytics and business intelligence solutions.",
    features: ["Data Visualization", "Business Intelligence", "Reporting"]
  },
  {
    icon: "☁️",
    title: "Cloud Solutions",
    description: "Scalable cloud infrastructure and deployment services.",
    features: ["AWS/Azure", "DevOps", "Scalability"]
  },
  {
    icon: "🎨",
    title: "System Design",
    description: "Beautiful, user-centered design for digital experiences.",
    features: ["User Research", "Prototyping", "Design Systems"]
  },
  {
    icon: "🔧",
    title: "DevOps & Deployment",
    description: "Streamlined deployment pipelines and infrastructure management.",
    features: ["CI/CD Pipelines", "Docker", "Monitoring"]
  },
  {
    icon: "📈",
    title: "Digital Marketing",
    description: "Comprehensive digital marketing strategies to grow your business.",
    features: ["SEO Optimization", "Social Media", "Content Strategy"]
  }
];

export const IMAGE_FALLBACKS = {
  mobile: [
    "/assets/services/images/Mobile.png",
    "/assets/services/images/mobile.jpg",
    "/assets/services/images/Mobile.jpg"
  ],
  web: [
    "/assets/services/images/Web.png",
    "/assets/services/images/web.jpg",
    "/assets/services/images/Web.jpg"
  ],
  ai: [
    "/assets/services/images/AI.png",
    "/assets/services/images/Ai.png",
    "/assets/services/images/ai.jpg",
    "/assets/services/images/AI.jpg"
  ]
};
