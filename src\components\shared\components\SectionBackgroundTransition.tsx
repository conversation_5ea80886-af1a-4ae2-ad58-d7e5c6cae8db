"use client";

import { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

export default function SectionBackgroundTransition() {
  const containerRef = useRef<HTMLDivElement>(null);
  const transitionRef = useRef<HTMLDivElement>(null);
  const particlesRef = useRef<HTMLDivElement>(null);
  const waveRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;
    const transition = transitionRef.current;
    const particles = particlesRef.current;
    const wave = waveRef.current;

    if (!container || !transition || !particles || !wave) return;

    // Create animated particles for transition
    const createTransitionParticles = () => {
      for (let i = 0; i < 20; i++) {
        const particle = document.createElement("div");
        particle.className = "transition-particle"; // Add class for easier targeting
        const originalColor = Math.random() > 0.5 ? '#302cff' : '#FFFFFF';
        particle.style.cssText = `
          position: absolute;
          width: ${Math.random() * 6 + 2}px;
          height: ${Math.random() * 6 + 2}px;
          background: ${originalColor};
          border-radius: 50%;
          left: ${Math.random() * 100}%;
          top: ${Math.random() * 100}%;
          opacity: 0;
          will-change: transform;
          transition: background-color 0.5s ease;
        `;
        particles.appendChild(particle);

        // Add scroll-triggered color change for mobile app section
        ScrollTrigger.create({
          trigger: "#mobile-app-showcase",
          start: "top 95%",
          end: "bottom 5%",
          onEnter: () => {
            particle.style.background = '#FFFFFF';
          },
          onLeave: () => {
            particle.style.background = originalColor;
          },
          onEnterBack: () => {
            particle.style.background = '#FFFFFF';
          },
          onLeaveBack: () => {
            particle.style.background = originalColor;
          }
        });
      }
    };

    // Create morphing wave effect
    const createMorphingWave = () => {
      const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
      svg.setAttribute("width", "100%");
      svg.setAttribute("height", "200px");
      svg.setAttribute("viewBox", "0 0 1200 200");
      svg.style.cssText = `
        position: absolute;
        bottom: 0;
        left: 0;
        z-index: 2;
      `;

      const path = document.createElementNS("http://www.w3.org/2000/svg", "path");
      path.setAttribute("fill", "url(#waveGradient)");
      
      const defs = document.createElementNS("http://www.w3.org/2000/svg", "defs");
      const gradient = document.createElementNS("http://www.w3.org/2000/svg", "linearGradient");
      gradient.setAttribute("id", "waveGradient");
      gradient.setAttribute("x1", "0%");
      gradient.setAttribute("y1", "0%");
      gradient.setAttribute("x2", "100%");
      gradient.setAttribute("y2", "0%");

      const stop1 = document.createElementNS("http://www.w3.org/2000/svg", "stop");
      stop1.setAttribute("offset", "0%");
      stop1.setAttribute("stop-color", "#302cff");
      stop1.setAttribute("stop-opacity", "0.8");

      const stop2 = document.createElementNS("http://www.w3.org/2000/svg", "stop");
      stop2.setAttribute("offset", "50%");
      stop2.setAttribute("stop-color", "#FFFFFF");
      stop2.setAttribute("stop-opacity", "0.6");

      const stop3 = document.createElementNS("http://www.w3.org/2000/svg", "stop");
      stop3.setAttribute("offset", "100%");
      stop3.setAttribute("stop-color", "#302cff");
      stop3.setAttribute("stop-opacity", "0.8");

      gradient.appendChild(stop1);
      gradient.appendChild(stop2);
      gradient.appendChild(stop3);
      defs.appendChild(gradient);
      svg.appendChild(defs);
      svg.appendChild(path);
      wave.appendChild(svg);

      return { path, svg };
    };

    createTransitionParticles();
    const { path } = createMorphingWave();

    // Main transition animation - smooth fade only, no waves
    ScrollTrigger.create({
      trigger: "#services",
      start: "bottom center",
      end: "bottom top",
      scrub: 1,
      onUpdate: (self) => {
        const progress = self.progress;

        // Background transition - smooth fade only
        gsap.set(transition, {
          background: `
            linear-gradient(135deg,
              rgba(10, 10, 10, ${1 - progress * 0.8}) 0%,
              rgba(147, 51, 234, ${progress * 0.2}) 30%,
              rgba(255, 255, 255, ${progress * 0.05}) 60%,
              rgba(186, 66, 255, ${progress * 0.15}) 100%
            )
          `,
          opacity: progress * 0.6
        });

        // Animate particles with subtle movement
        const particleElements = particles.children;
        Array.from(particleElements).forEach((particle, index) => {
          gsap.set(particle, {
            opacity: progress * 0.4,
            y: -progress * 50,
            x: Math.sin(progress * Math.PI + index) * 20,
            scale: 0.8 + progress * 0.4,
            rotation: progress * 180
          });
        });

        // Remove wave animation - keep path hidden
        path.setAttribute("d", "M 0 0 L 0 0 Z");
      }
    });

    // Particle burst animation when entering mobile app section
    ScrollTrigger.create({
      trigger: "#mobile-app-showcase",
      start: "top 95%",
      onEnter: () => {
        const particleElements = particles.children;
        Array.from(particleElements).forEach((particle, index) => {
          gsap.fromTo(particle, 
            { 
              scale: 0,
              opacity: 0,
              x: "50%",
              y: "50%"
            },
            {
              scale: 1.5,
              opacity: 0.8,
              x: `random(-200, 200)`,
              y: `random(-200, 200)`,
              duration: 2,
              delay: index * 0.1,
              ease: "power2.out",
              onComplete: () => {
                gsap.to(particle, {
                  opacity: 0,
                  scale: 0,
                  duration: 1,
                  ease: "power2.in"
                });
              }
            }
          );
        });
      }
    });

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  return (
    <div ref={containerRef} className="fixed inset-0 pointer-events-none z-[5]">
      {/* Main transition overlay */}
      <div 
        ref={transitionRef}
        className="absolute inset-0 opacity-0"
        style={{
          background: `
            linear-gradient(135deg, 
              rgba(10, 10, 10, 1) 0%, 
              rgba(48, 44, 255, 0) 30%,
              rgba(255, 255, 255, 0) 60%,
              rgba(48, 44, 255, 0) 100%
            )
          `
        }}
      />
      
      {/* Animated particles */}
      <div ref={particlesRef} className="absolute inset-0" />
      
      {/* Morphing wave */}
      <div ref={waveRef} className="absolute inset-0" />
      
      {/* Additional gradient overlays */}
      <div className="absolute inset-0 bg-gradient-to-t from-[#302cff]/20 via-transparent to-transparent opacity-0 transition-opacity duration-1000" />
    </div>
  );
}
