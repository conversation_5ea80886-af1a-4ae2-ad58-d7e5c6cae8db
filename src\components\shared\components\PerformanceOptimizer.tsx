"use client";

import { useEffect } from 'react';

export default function PerformanceOptimizer() {
  useEffect(() => {
    // CRITICAL: Preload essential fonts for better LCP
    const preloadFont = (href: string, as: string = 'font') => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = href;
      link.as = as;
      link.type = 'font/woff2';
      link.crossOrigin = 'anonymous';
      document.head.appendChild(link);
    };

    // Preload critical fonts
    preloadFont('/fonts/Orbitron-Regular.woff2');
    preloadFont('/fonts/Sora-Regular.woff2');

    // REMOVED: Image optimization that caused re-rendering issues

    // CRITICAL: Reduce layout shifts
    const preventLayoutShifts = () => {
      // Add aspect ratio containers for images
      const containers = document.querySelectorAll('[data-aspect-ratio]');
      containers.forEach(container => {
        const ratio = container.getAttribute('data-aspect-ratio');
        if (ratio) {
          (container as HTMLElement).style.aspectRatio = ratio;
        }
      });
    };

    // PERFORMANCE: Optimize animations for 60fps
    const optimizeAnimations = () => {
      // Reduce motion for users who prefer it
      if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
        document.documentElement.style.setProperty('--animation-duration', '0.01ms');
        document.documentElement.style.setProperty('--transition-duration', '0.01ms');
      }
    };

    // CRITICAL: Defer non-critical resources
    const deferNonCriticalResources = () => {
      // Defer third-party scripts
      const scripts = document.querySelectorAll('script[data-defer]');
      scripts.forEach(script => {
        script.setAttribute('defer', '');
      });
    };

    // PERFORMANCE: Optimize scroll performance
    const optimizeScrolling = () => {
      // Add passive event listeners hint
      document.addEventListener('touchstart', () => {}, { passive: true });
      document.addEventListener('touchmove', () => {}, { passive: true });
      document.addEventListener('wheel', () => {}, { passive: true });
    };

    // Execute optimizations
    preventLayoutShifts();
    optimizeAnimations();
    deferNonCriticalResources();
    optimizeScrolling();

    // CRITICAL: Resource hints for better loading
    const addResourceHints = () => {
      // DNS prefetch for external resources
      const dnsPrefetch = (href: string) => {
        const link = document.createElement('link');
        link.rel = 'dns-prefetch';
        link.href = href;
        document.head.appendChild(link);
      };

      // Preconnect to critical origins
      const preconnect = (href: string) => {
        const link = document.createElement('link');
        link.rel = 'preconnect';
        link.href = href;
        link.crossOrigin = 'anonymous';
        document.head.appendChild(link);
      };

      // Add resource hints
      dnsPrefetch('https://prod.spline.design');
      preconnect('https://prod.spline.design');
    };

    addResourceHints();

    // PERFORMANCE: Monitor and report performance metrics
    const reportPerformanceMetrics = () => {
      if ('PerformanceObserver' in window) {
        // Monitor LCP
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          console.log('LCP:', lastEntry.startTime);
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

        // Monitor FID
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach(entry => {
            const fidEntry = entry as PerformanceEventTiming;
            if (fidEntry.processingStart) {
              console.log('FID:', fidEntry.processingStart - fidEntry.startTime);
            }
          });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });

        // Monitor CLS
        const clsObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach(entry => {
            const clsEntry = entry as PerformanceEntry & { hadRecentInput?: boolean; value?: number };
            if (!clsEntry.hadRecentInput && clsEntry.value !== undefined) {
              console.log('CLS:', clsEntry.value);
            }
          });
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
      }
    };

    // Report metrics in development
    if (process.env.NODE_ENV === 'development') {
      reportPerformanceMetrics();
    }

  }, []);

  return null; // This component doesn't render anything
}
