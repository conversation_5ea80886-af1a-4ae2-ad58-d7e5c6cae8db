import Image from "next/image";

interface LogoSectionProps {
  logoRef: React.RefObject<HTMLDivElement | null>;
}

export default function LogoSection({ logoRef }: LogoSectionProps) {
  return (
    <div ref={logoRef} className="flex justify-center lg:justify-end items-start">
      <Image
        src="/assets/BlueLogo.png"
        alt="TregoTech Blue Logo"
        width={320}
        height={320}
        priority
        className="w-80 h-auto max-w-full"
      />
    </div>
  );
}
