"use client";

import lottie from 'lottie-web';
import { useEffect, useRef } from 'react';

interface LottieAnimationProps {
  animationData: unknown;
  className?: string;
  loop?: boolean;
  autoplay?: boolean;
  renderer?: 'svg' | 'canvas' | 'html';
  style?: React.CSSProperties;
}

export default function LottieAnimation({ 
  animationData, 
  className = "",
  loop = true,
  autoplay = true,
  renderer = 'svg',
  style = {}
}: LottieAnimationProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<{ destroy: () => void } | null>(null);

  useEffect(() => {
    if (!containerRef.current || !animationData) return;

    // Destroy previous animation if it exists
    if (animationRef.current) {
      animationRef.current.destroy();
    }

    // Create new animation
    animationRef.current = lottie.loadAnimation({
      container: containerRef.current,
      renderer: renderer,
      loop: loop,
      autoplay: autoplay,
      animationData: animationData,
      rendererSettings: {
        preserveAspectRatio: 'xMidYMid slice',
        progressiveLoad: true,
        // Optimize for performance
        clearCanvas: true,
        hideOnTransparent: true,
      },
    });

    // Cleanup function
    return () => {
      if (animationRef.current) {
        animationRef.current.destroy();
        animationRef.current = null;
      }
    };
  }, [animationData, loop, autoplay, renderer]);

  return (
    <div
      ref={containerRef}
      className={className}
      style={{
        willChange: 'transform',
        backfaceVisibility: 'hidden',
        transform: 'translateZ(0)',
        ...style
      }}
    />
  );
}
