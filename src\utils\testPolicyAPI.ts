// Test utility to verify the policy API is working
export const testPolicyAPI = async () => {
  try {
    console.log('🧪 Testing Policy API...');
    
    const response = await fetch('https://prod.tregotech.com/tr/admin/ourPolicy/getVisiblePolicy');
    
    console.log('📡 Response status:', response.status);
    console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    
    console.log('✅ API Response received');
    console.log('📊 Data structure:', {
      hasData: !!data.data,
      isArray: Array.isArray(data.data),
      itemCount: data.data?.length || 0,
      firstItem: data.data?.[0] ? {
        id: data.data[0].id,
        hasTitle: !!(data.data[0].title_en || data.data[0].title_ar),
        hasDescription: !!(data.data[0].description_en || data.data[0].description_ar),
        status: data.data[0].status,
        type: data.data[0].type
      } : null
    });
    
    return data;
  } catch (error) {
    console.error('❌ API Test failed:', error);
    throw error;
  }
};

// Test utility to verify the terms API is working
export const testTermsAPI = async () => {
  try {
    console.log('🧪 Testing Terms API...');

    const response = await fetch('https://prod.tregotech.com/tr/admin/terms/getVisibleTerms');

    console.log('📡 Response status:', response.status);
    console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    console.log('✅ Terms API Response received');
    console.log('📊 Data structure:', {
      hasData: !!data.data,
      isArray: Array.isArray(data.data),
      itemCount: data.data?.length || 0,
      firstItem: data.data?.[0] ? {
        id: data.data[0].id,
        hasTitle: !!(data.data[0].title_en || data.data[0].title_ar),
        hasDescription: !!(data.data[0].description_en || data.data[0].description_ar),
        status: data.data[0].status,
        type: data.data[0].type
      } : null
    });

    return data;
  } catch (error) {
    console.error('❌ Terms API Test failed:', error);
    throw error;
  }
};

// Run test in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // Uncomment the lines below to test the APIs in browser console
  // testPolicyAPI();
  // testTermsAPI();
}
