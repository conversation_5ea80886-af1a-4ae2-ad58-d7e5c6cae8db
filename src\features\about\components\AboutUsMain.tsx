"use client";

import { useAboutUs } from "../hooks/useAboutUs";
import AboutTitle from "./AboutTitle";
import VisionSection from "./VisionSection";
import MissionSection from "./MissionSection";
import LogoSection from "./LogoSection";
import ValuesSection from "./ValuesSection";
import ContactSection from "./ContactSection";

export default function AboutUsMain() {
  const { refs } = useAboutUs();

  return (
    <div ref={refs.containerRef} className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 w-72 h-72 bg-[#302cff]/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-[#302cff]/5 rounded-full blur-3xl"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 pt-32 pb-16 px-8 lg:px-16">
        <div className="max-w-7xl mx-auto">
          
          {/* About Us Title */}
          <AboutTitle titleRef={refs.titleRef} />

          {/* Main Content Grid - Logo Right, Vision & Mission Left */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-start">
            
            {/* Left Side - Vision and Mission */}
            <div className="space-y-16">
              <VisionSection visionRef={refs.visionRef} />
              <MissionSection missionRef={refs.missionRef} />
            </div>

            {/* Right Side - Logo */}
            <LogoSection logoRef={refs.logoRef} />
          </div>

          {/* Our Values Section */}
          <ValuesSection valuesRef={refs.valuesRef} />

          {/* Contact Us Section */}
          <ContactSection contactRef={refs.contactRef} />

        </div>
      </div>
    </div>
  );
}
