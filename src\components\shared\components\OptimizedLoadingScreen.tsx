"use client";

import { useOptimizedLoading } from "@/hooks/useOptimizedLoading";

export default function OptimizedLoadingScreen() {
  const { isLoading } = useOptimizedLoading();

  if (!isLoading) return null;

  return (
    <div
      data-loading-screen
      className="fixed inset-0 z-50 bg-gradient-to-br from-[#0a0a0a] via-[#1a1a2e] to-[#16213e] flex items-center justify-center overflow-hidden"
    >
      {/* Animated Background Particles */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-2 h-2 bg-[#BA42FF] rounded-full animate-float-1 opacity-60"></div>
        <div className="absolute top-40 right-32 w-1 h-1 bg-[#00E1FF] rounded-full animate-float-2 opacity-80"></div>
        <div className="absolute bottom-32 left-40 w-3 h-3 bg-[#302cff] rounded-full animate-float-3 opacity-40"></div>
        <div className="absolute bottom-20 right-20 w-2 h-2 bg-[#BA42FF] rounded-full animate-float-4 opacity-70"></div>
        <div className="absolute top-60 left-1/3 w-1 h-1 bg-[#00E1FF] rounded-full animate-float-5 opacity-90"></div>
        <div className="absolute top-80 right-1/4 w-2 h-2 bg-[#302cff] rounded-full animate-float-6 opacity-50"></div>
      </div>

      {/* Main Logo Animation */}
      <div className="relative flex items-center justify-center">

        {/* Outer Rotating Ring */}
        <div className="absolute w-80 h-80 border-2 border-[#302cff]/20 rounded-full animate-spin-slow">
          <div className="absolute -top-1 left-1/2 w-2 h-2 bg-[#BA42FF] rounded-full transform -translate-x-1/2 animate-pulse shadow-[0_0_15px_rgba(186,66,255,0.8)]"></div>
          <div className="absolute -bottom-1 left-1/2 w-2 h-2 bg-[#00E1FF] rounded-full transform -translate-x-1/2 animate-pulse shadow-[0_0_15px_rgba(0,225,255,0.8)]"></div>
          <div className="absolute top-1/2 -left-1 w-2 h-2 bg-[#302cff] rounded-full transform -translate-y-1/2 animate-pulse shadow-[0_0_15px_rgba(48,44,255,0.8)]"></div>
          <div className="absolute top-1/2 -right-1 w-2 h-2 bg-[#BA42FF] rounded-full transform -translate-y-1/2 animate-pulse shadow-[0_0_15px_rgba(186,66,255,0.8)]"></div>
        </div>

        {/* Middle Rotating Ring - Counter Direction */}
        <div className="absolute w-60 h-60 border border-[#BA42FF]/30 rounded-full animate-spin-reverse">
          <div className="absolute -top-0.5 left-1/2 w-1.5 h-1.5 bg-[#00E1FF] rounded-full transform -translate-x-1/2 animate-pulse"></div>
          <div className="absolute -bottom-0.5 left-1/2 w-1.5 h-1.5 bg-[#302cff] rounded-full transform -translate-x-1/2 animate-pulse"></div>
        </div>

        {/* Inner Pulsing Ring */}
        <div className="absolute w-40 h-40 border border-[#00E1FF]/40 rounded-full animate-pulse-ring">
          <div className="absolute inset-2 border border-[#BA42FF]/20 rounded-full animate-pulse-ring-inner"></div>
        </div>

        {/* Central Logo Container */}
        <div className="relative bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl rounded-full p-8 border border-[#302cff]/40 animate-logo-morph shadow-[0_0_50px_rgba(48,44,255,0.3)]">
          <div className="text-center">
            <h1 className="text-3xl md:text-4xl font-bold text-white font-orbitron mb-2 animate-logo-text-wave">
              <span className="inline-block animate-letter-bounce" style={{animationDelay: '0s'}}>T</span>
              <span className="inline-block animate-letter-bounce" style={{animationDelay: '0.1s'}}>r</span>
              <span className="inline-block animate-letter-bounce" style={{animationDelay: '0.2s'}}>e</span>
              <span className="inline-block animate-letter-bounce" style={{animationDelay: '0.3s'}}>g</span>
              <span className="inline-block animate-letter-bounce" style={{animationDelay: '0.4s'}}>o</span>
              <span className="inline-block text-[#302cff] animate-letter-bounce ml-1" style={{animationDelay: '0.5s'}}>T</span>
              <span className="inline-block text-[#302cff] animate-letter-bounce" style={{animationDelay: '0.6s'}}>e</span>
              <span className="inline-block text-[#302cff] animate-letter-bounce" style={{animationDelay: '0.7s'}}>c</span>
              <span className="inline-block text-[#302cff] animate-letter-bounce" style={{animationDelay: '0.8s'}}>h</span>
            </h1>
            <div className="w-12 h-0.5 bg-gradient-to-r from-[#BA42FF] to-[#00E1FF] mx-auto rounded-full animate-line-pulse"></div>
          </div>
        </div>

        {/* Energy Waves */}
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute inset-0 border-2 border-[#BA42FF]/10 rounded-full animate-energy-wave-1"></div>
          <div className="absolute inset-4 border-2 border-[#00E1FF]/10 rounded-full animate-energy-wave-2"></div>
          <div className="absolute inset-8 border-2 border-[#302cff]/10 rounded-full animate-energy-wave-3"></div>
        </div>

      </div>
    </div>
  );
}
