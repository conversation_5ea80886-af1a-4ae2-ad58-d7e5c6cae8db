interface TermsSection {
  id: string;
  title: string;
  content: string[];
}

interface TermsContentProps {
  contentRef: React.RefObject<HTMLDivElement>;
  termsData: TermsSection[];
  loading: boolean;
}

export default function TermsContent({ contentRef, termsData, loading }: TermsContentProps) {
  if (loading) {
    return (
      <div ref={contentRef} className="text-center py-20">
        <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-[#302cff]"></div>
        <p className="text-gray-300 mt-4 font-sora">Loading terms...</p>
      </div>
    );
  }

  return (
    <div ref={contentRef} className="space-y-8">
      {termsData.map((section) => (
        <div key={section.id} className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-xl rounded-3xl p-8 md:p-12 border border-gray-700/30 shadow-2xl">
          <h2 className="text-2xl md:text-3xl font-bold text-white mb-6 font-orbitron">
            {section.title}
          </h2>
          
          <div className="space-y-4">
            {section.content.map((paragraph, paragraphIndex) => (
              <p key={paragraphIndex} className="text-gray-300 font-sora leading-relaxed text-base md:text-lg">
                {paragraph}
              </p>
            ))}
          </div>
        </div>
      ))}
      
      {/* Footer */}
      <div className="text-center mt-16 pt-8 border-t border-gray-700/30">
        <div className="bg-gradient-to-br from-gray-800/30 to-gray-900/30 backdrop-blur-sm rounded-2xl p-6">
          <p className="text-gray-400 font-sora mb-2">
            © 2024 TregoTech. All rights reserved.
          </p>
          <p className="text-gray-500 text-sm font-sora">
            These terms are effective as of the date last updated above.
          </p>
        </div>
      </div>
    </div>
  );
}
