'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { usePathname } from 'next/navigation'

export default function Navbar() {
  const pathname = usePathname()
  const [scrolled, setScrolled] = useState(false)
  const [isHomeDropdownOpen, setIsHomeDropdownOpen] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [activeTab, setActiveTab] = useState('home')

  useEffect(() => {
    const handleScroll = () => {
      requestAnimationFrame(() => {
        setScrolled(window.scrollY > 50)
      })
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Set active tab based on current pathname
  useEffect(() => {
    if (pathname === '/about') {
      setActiveTab('about')
    } else if (pathname === '/contact') {
      setActiveTab('contact')
    } else {
      setActiveTab('home')
    }
  }, [pathname])

  // Handle navigation to sections from URL hash
  useEffect(() => {
    if (pathname === '/' && window.location.hash) {
      const sectionId = window.location.hash.substring(1);
      setTimeout(() => {
        const element = document.getElementById(sectionId);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      }, 100); // Small delay to ensure page is loaded
    }
  }, [pathname])

  const scrollToSection = (id: string) => {
    // If we're not on the home page, navigate to home first
    if (pathname !== '/') {
      window.location.href = `/#${id}`;
      return;
    }

    const el = document.getElementById(id)
    if (el) {
      el.scrollIntoView({ behavior: 'smooth' })
    }
    setIsHomeDropdownOpen(false)
    setIsMobileMenuOpen(false)
  }

  const homeDropdownItems = [
    {
      label: 'Services',
      id: 'services',
      icon: '🛠️',
      description: 'Our development services'
    },
    {
      label: 'Become a Partner',
      id: 'become-partner',
      icon: '🤝',
      description: 'Join our partner network'
    },
    {
      label: 'Trego App',
      id: 'mobile-app-showcase',
      icon: '/assets/trego_app/logo.png',
      description: 'Our mobile application',
      isImage: true
    }
  ]

  return (
    <nav
      className={`fixed top-0 w-full z-50 ${
        scrolled ? 'bg-[#0f0f0f]/90 backdrop-blur-lg shadow-md' : 'bg-transparent'
      } transition-colors duration-300`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 py-4 flex justify-between items-center">
        {/* Logo */}
        <Link href="/">
          <span
            className="text-white text-xl sm:text-2xl font-bold tracking-widest cursor-pointer"
            data-cursor="pointer"
          >
            Trego<span className="text-[#302cff]">Tech</span>
          </span>
        </Link>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center space-x-8">
          {/* Home Dropdown */}
          <div
            className="relative"
            onMouseEnter={() => setIsHomeDropdownOpen(true)}
            onMouseLeave={() => setIsHomeDropdownOpen(false)}
          >
            <Link
              href="/"
              onClick={() => setActiveTab('home')}
              className="text-white text-sm font-medium hover:text-[#302cff] transition-colors duration-200 cursor-pointer relative py-2 block"
              data-cursor="pointer"
            >
              Home
              {activeTab === 'home' && (
                <div className="absolute bottom-0 left-0 w-full h-0.5 bg-[#302cff] rounded-full"></div>
              )}
            </Link>

            {/* Dropdown Menu */}
            <div className={`absolute top-full left-1/2 transform -translate-x-1/2 mt-2 w-[600px] bg-[#0f0f0f]/95 backdrop-blur-xl rounded-2xl border border-[#302cff]/20 shadow-2xl transition-all duration-300 ${
              isHomeDropdownOpen
                ? 'opacity-100 visible translate-y-0'
                : 'opacity-0 invisible -translate-y-2'
            }`}>
              <div className="p-6">
                <div className="grid grid-cols-3 gap-4">
                  {homeDropdownItems.map((item, index) => (
                    <button
                      key={item.id}
                      onClick={() => scrollToSection(item.id)}
                      className={`flex flex-col items-center text-center p-4 rounded-xl hover:bg-[#302cff]/10 transition-all duration-200 group transform hover:scale-[1.05] ${
                        isHomeDropdownOpen
                          ? 'animate-slideInUp'
                          : ''
                      }`}
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      <div className="w-12 h-12 bg-gradient-to-br from-[#302cff]/20 to-[#1e1a99]/20 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200 mb-3">
                        {item.isImage ? (
                          <Image
                            src={item.icon}
                            alt={item.label}
                            width={28}
                            height={28}
                            className="object-contain"
                          />
                        ) : (
                          <span className="text-xl">{item.icon}</span>
                        )}
                      </div>
                      <h3 className="text-white font-semibold text-sm group-hover:text-[#302cff] transition-colors duration-200 mb-1">
                        {item.label}
                      </h3>
                      <p className="text-gray-400 text-xs leading-relaxed">
                        {item.description}
                      </p>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* About Us */}
          <Link
            href="/about"
            onClick={() => setActiveTab('about')}
            className="text-white text-sm font-medium hover:text-[#302cff] transition-colors duration-200 cursor-pointer relative py-2 block"
            data-cursor="pointer"
          >
            About Us
            {activeTab === 'about' && (
              <div className="absolute bottom-0 left-0 w-full h-0.5 bg-[#302cff] rounded-full"></div>
            )}
          </Link>

          {/* Contact Us */}
          <Link
            href="/contact"
            onClick={() => setActiveTab('contact')}
            className="text-white text-sm font-medium hover:text-[#302cff] transition-colors duration-200 cursor-pointer relative py-2 block"
            data-cursor="pointer"
          >
            Contact Us
            {activeTab === 'contact' && (
              <div className="absolute bottom-0 left-0 w-full h-0.5 bg-[#302cff] rounded-full"></div>
            )}
          </Link>
        </div>

        {/* Mobile Menu Button */}
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="md:hidden text-white p-2 rounded-lg hover:bg-white/10 transition-colors duration-200"
          data-cursor="pointer"
        >
          <svg
            className={`w-6 h-6 transition-transform duration-200 ${isMobileMenuOpen ? 'rotate-45' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            {isMobileMenuOpen ? (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            ) : (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            )}
          </svg>
        </button>
      </div>

      {/* Mobile Menu */}
      <div className={`md:hidden transition-all duration-300 ${
        isMobileMenuOpen
          ? 'max-h-screen opacity-100'
          : 'max-h-0 opacity-0 overflow-hidden'
      }`}>
        <div className="bg-[#0f0f0f]/95 backdrop-blur-xl border-t border-[#302cff]/20">
          <div className="px-4 py-6 space-y-4">
            {/* Home Section with Subsections */}
            <div className="space-y-3">
              <Link
                href="/"
                onClick={() => setIsMobileMenuOpen(false)}
                className="block text-white text-lg font-semibold hover:text-[#302cff] transition-colors duration-200"
              >
                Home
              </Link>
              <div className="pl-4 space-y-3 border-l-2 border-[#302cff]/30">
                {homeDropdownItems.map((item) => (
                  <button
                    key={item.id}
                    onClick={() => scrollToSection(item.id)}
                    className="flex items-center space-x-3 w-full text-left p-3 rounded-lg hover:bg-[#302cff]/10 transition-all duration-200 group"
                  >
                    <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-[#302cff]/20 to-[#1e1a99]/20 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                      {item.isImage ? (
                        <Image
                          src={item.icon}
                          alt={item.label}
                          width={20}
                          height={20}
                          className="object-contain"
                        />
                      ) : (
                        <span className="text-sm">{item.icon}</span>
                      )}
                    </div>
                    <div>
                      <h3 className="text-white font-medium text-sm group-hover:text-[#302cff] transition-colors duration-200">
                        {item.label}
                      </h3>
                      <p className="text-gray-400 text-xs">
                        {item.description}
                      </p>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* About Us */}
            <Link
              href="/about"
              onClick={() => setIsMobileMenuOpen(false)}
              className="block text-white text-lg font-semibold hover:text-[#302cff] transition-colors duration-200 py-2"
            >
              About Us
            </Link>

            {/* Contact Us */}
            <Link
              href="/contact"
              onClick={() => setIsMobileMenuOpen(false)}
              className="block text-white text-lg font-semibold hover:text-[#302cff] transition-colors duration-200 py-2"
            >
              Contact Us
            </Link>
          </div>
        </div>
      </div>
    </nav>
  )
}
