"use client";

import { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Register ScrollTrigger plugin
if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

export default function InsaneBackgroundEffects() {
  const containerRef = useRef<HTMLDivElement>(null);
  const particlesRef = useRef<HTMLDivElement[]>([]);
  const waveRef = useRef<HTMLDivElement>(null);
  const matrixRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Create floating particles with scroll interaction
    const createScrollParticles = () => {
      for (let i = 0; i < 100; i++) {
        const particle = document.createElement("div");
        particle.className = "scroll-particle";
        particle.style.cssText = `
          position: fixed;
          width: ${Math.random() * 6 + 2}px;
          height: ${Math.random() * 6 + 2}px;
          background: ${Math.random() > 0.5 ? '#BA42FF' : '#00E1FF'};
          border-radius: 50%;
          opacity: ${Math.random() * 0.8 + 0.2};
          left: ${Math.random() * 100}vw;
          top: ${Math.random() * 100}vh;
          pointer-events: none;
          z-index: 1;
        `;
        container.appendChild(particle);
        particlesRef.current.push(particle);

        // Scroll-triggered particle movement
        ScrollTrigger.create({
          trigger: "body",
          start: "top top",
          end: "bottom bottom",
          scrub: true,
          onUpdate: (self) => {
            const progress = self.progress;
            gsap.set(particle, {
              y: progress * (Math.random() * 2000 - 1000),
              x: Math.sin(progress * Math.PI * 4 + i) * 200,
              rotation: progress * 360 * (Math.random() > 0.5 ? 1 : -1),
              scale: 0.5 + progress * 1.5,
            });
          },
        });
      }
    };

    // Create morphing wave effect
    const createMorphingWave = () => {
      if (!waveRef.current) return;
      
      ScrollTrigger.create({
        trigger: "body",
        start: "top top",
        end: "bottom bottom",
        scrub: 1,
        onUpdate: (self) => {
          const progress = self.progress;
          gsap.set(waveRef.current, {
            background: `radial-gradient(circle at ${50 + Math.sin(progress * Math.PI * 4) * 30}% ${50 + Math.cos(progress * Math.PI * 6) * 40}%, 
              rgba(186, 66, 255, ${0.1 + progress * 0.2}) 0%, 
              rgba(0, 225, 255, ${0.05 + progress * 0.15}) 50%, 
              transparent 70%)`,
            transform: `scale(${1 + progress * 2}) rotate(${progress * 180}deg)`,
          });
        },
      });
    };

    // Create matrix rain effect
    const createMatrixRain = () => {
      if (!matrixRef.current) return;
      
      const chars = "01ABCDEFGHIJKLMNOPQRSTUVWXYZ";
      const columns = Math.floor(window.innerWidth / 20);
      
      for (let i = 0; i < columns; i++) {
        const column = document.createElement("div");
        column.style.cssText = `
          position: absolute;
          left: ${i * 20}px;
          top: 0;
          color: #00E1FF;
          font-family: monospace;
          font-size: 14px;
          opacity: 0.3;
          white-space: pre;
        `;
        matrixRef.current.appendChild(column);

        // Animate matrix characters
        const animateColumn = () => {
          let text = "";
          for (let j = 0; j < 50; j++) {
            text += chars[Math.floor(Math.random() * chars.length)] + "\n";
          }
          column.textContent = text;
          
          gsap.fromTo(column, 
            { y: -1000 },
            { 
              y: window.innerHeight + 100,
              duration: Math.random() * 10 + 5,
              ease: "none",
              onComplete: animateColumn,
            }
          );
        };
        
        setTimeout(animateColumn, Math.random() * 5000);
      }
    };

    // Create energy pulses
    const createEnergyPulses = () => {
      for (let i = 0; i < 5; i++) {
        const pulse = document.createElement("div");
        pulse.style.cssText = `
          position: fixed;
          width: 200px;
          height: 200px;
          border: 2px solid ${i % 2 === 0 ? '#BA42FF' : '#00E1FF'};
          border-radius: 50%;
          opacity: 0;
          pointer-events: none;
          z-index: 2;
        `;
        container.appendChild(pulse);

        ScrollTrigger.create({
          trigger: "body",
          start: "top top",
          end: "bottom bottom",
          scrub: 1,
          onUpdate: (self) => {
            const progress = self.progress;
            gsap.set(pulse, {
              left: `${20 + i * 15}%`,
              top: `${30 + Math.sin(progress * Math.PI * 2 + i) * 40}%`,
              scale: 0.5 + progress * 2,
              opacity: Math.sin(progress * Math.PI * 3 + i) * 0.5 + 0.3,
              rotation: progress * 360,
            });
          },
        });
      }
    };

    // Create lightning effects
    const createLightningEffects = () => {
      const createLightning = () => {
        const lightning = document.createElement("div");
        lightning.style.cssText = `
          position: fixed;
          width: 2px;
          height: 100vh;
          background: linear-gradient(180deg, transparent, #00E1FF, transparent);
          left: ${Math.random() * 100}vw;
          top: 0;
          opacity: 0;
          pointer-events: none;
          z-index: 3;
        `;
        container.appendChild(lightning);

        gsap.timeline()
          .to(lightning, { opacity: 1, duration: 0.1 })
          .to(lightning, { opacity: 0, duration: 0.1, delay: 0.1 })
          .to(lightning, { opacity: 0.8, duration: 0.05, delay: 0.05 })
          .to(lightning, { opacity: 0, duration: 0.2 })
          .call(() => lightning.remove());
      };

      // Random lightning strikes
      const lightningInterval = setInterval(() => {
        if (Math.random() > 0.7) {
          createLightning();
        }
      }, 2000);

      return () => clearInterval(lightningInterval);
    };

    createScrollParticles();
    createMorphingWave();
    createMatrixRain();
    createEnergyPulses();
    const cleanupLightning = createLightningEffects();

    return () => {
      cleanupLightning();
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
      particlesRef.current.forEach(particle => {
        if (particle.parentNode) {
          particle.parentNode.removeChild(particle);
        }
      });
      particlesRef.current = [];
    };
  }, []);

  return (
    <div ref={containerRef} className="fixed inset-0 pointer-events-none overflow-hidden z-0">
      {/* Morphing Wave Background */}
      <div
        ref={waveRef}
        className="absolute inset-0 w-full h-full"
        style={{
          background: "radial-gradient(circle at 50% 50%, rgba(186, 66, 255, 0.1) 0%, rgba(0, 225, 255, 0.05) 50%, transparent 70%)",
        }}
      />
      
      {/* Matrix Rain Container */}
      <div ref={matrixRef} className="absolute inset-0 overflow-hidden opacity-20" />
      
      {/* Base gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#0a0a0a] via-[#1a1a2e] to-[#16213e] opacity-90" />
    </div>
  );
}
