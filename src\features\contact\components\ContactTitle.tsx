interface ContactTitleProps {
  titleRef: React.RefObject<HTMLDivElement | null>;
}

export default function ContactTitle({ titleRef }: ContactTitleProps) {
  return (
    <div ref={titleRef} className="text-center mb-16">
      <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 font-orbitron leading-tight">
        Contact <span className="text-[#302cff]">Us</span>
      </h1>
      <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto font-sora leading-relaxed">
        Ready to start your next project? Get in touch with our team and let&apos;s build something amazing together.
      </p>
    </div>
  );
}
