"use client";

import { useEffect, useRef, useState } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

export default function FuturisticBackground() {
  const containerRef = useRef<HTMLDivElement>(null);
  const gridRef = useRef<HTMLDivElement>(null);
  const gradientRef = useRef<HTMLDivElement>(null);
  const radialGradient1Ref = useRef<HTMLDivElement>(null);
  const radialGradient2Ref = useRef<HTMLDivElement>(null);
  const radialGradient3Ref = useRef<HTMLDivElement>(null);
  const circuitRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const animationsRef = useRef<gsap.core.Timeline[]>([]);
  
  useEffect(() => {
    // Delay background animations until after initial load
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (!isVisible) return;

    const container = containerRef.current;
    if (!container) return;

    // Reduced particle count for better performance
    const createOptimizedParticles = () => {
      const fragment = document.createDocumentFragment();

      for (let i = 0; i < 8; i++) { // Further reduced from 15 to 8 for better performance
        const particle = document.createElement("div");
        particle.className = "futuristic-particle";
        const originalColor = Math.random() > 0.5 ? '#BA42FF' : '#00E1FF';
        particle.style.cssText = `
          position: absolute;
          width: 2px;
          height: 2px;
          background: ${originalColor};
          border-radius: 50%;
          opacity: ${Math.random() * 0.3 + 0.1};
          left: ${Math.random() * 100}%;
          top: ${Math.random() * 100}%;
          will-change: transform;
          transition: background-color 0.8s ease;
        `;
        fragment.appendChild(particle);

        // Single combined animation for better performance
        const tl = gsap.timeline({ repeat: -1 });
        tl.to(particle, {
          x: `random(-50, 50)`,
          y: `random(-50, 50)`,
          opacity: `random(0.1, 0.4)`,
          duration: `random(8, 15)`,
          ease: "sine.inOut",
          yoyo: true,
        });

        animationsRef.current.push(tl);

        // Add scroll-triggered color change for Trego app section
        ScrollTrigger.create({
          trigger: "#mobile-app-showcase",
          start: "top 95%",
          end: "bottom 5%",
          onEnter: () => {
            particle.style.background = '#302cff'; // Dark blue bubbles in Trego section
          },
          onLeave: () => {
            particle.style.background = originalColor;
          },
          onEnterBack: () => {
            particle.style.background = '#302cff'; // Dark blue bubbles in Trego section
          },
          onLeaveBack: () => {
            particle.style.background = originalColor;
          }
        });
      }

      container.appendChild(fragment);
    };

    createOptimizedParticles();

    // Create transition overlay for smooth morphing effect
    const createTransitionOverlay = () => {
      const overlay = document.createElement("div");
      overlay.className = "transition-overlay";
      overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0) 100%);
        opacity: 0;
        pointer-events: none;
        z-index: 1;
        transition: all 0.8s ease;
      `;
      container.appendChild(overlay);
      return overlay;
    };

    const transitionOverlay = createTransitionOverlay();

    // Add ScrollTrigger for main background theme change with smooth transition
    const createBackgroundThemeChange = () => {
      // Create smooth progressive transition using scrub
      ScrollTrigger.create({
        trigger: "#mobile-app-showcase",
        start: "top 100%",
        end: "top 20%",
        scrub: 1.5, // Smooth scrubbing for gradual transition
        onUpdate: (self) => {
          const progress = self.progress;

          // Smooth grid transition
          if (gridRef.current) {
            const purpleOpacity = 0.1 * (1 - progress);
            const whiteOpacity = 0.1 * progress;
            gridRef.current.style.backgroundImage = `
              linear-gradient(rgba(186, 66, 255, ${purpleOpacity}) 1px, transparent 1px),
              linear-gradient(90deg, rgba(186, 66, 255, ${purpleOpacity}) 1px, transparent 1px),
              linear-gradient(rgba(255, 255, 255, ${whiteOpacity}) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255, 255, 255, ${whiteOpacity}) 1px, transparent 1px)
            `;
          }

          // Smooth background gradient transition
          if (gradientRef.current) {
            const darkR = 10 + (255 - 10) * progress;
            const darkG = 10 + (255 - 10) * progress;
            const darkB = 10 + (255 - 10) * progress;
            const midR = 26 + (248 - 26) * progress;
            const midG = 26 + (250 - 26) * progress;
            const midB = 46 + (252 - 46) * progress;
            const endR = 22 + (255 - 22) * progress;
            const endG = 33 + (255 - 33) * progress;
            const endB = 62 + (255 - 62) * progress;

            gradientRef.current.style.background = `linear-gradient(135deg,
              rgba(${darkR}, ${darkG}, ${darkB}, ${0.95}) 0%,
              rgba(${midR}, ${midG}, ${midB}, ${0.9}) 50%,
              rgba(${endR}, ${endG}, ${endB}, ${0.95}) 100%)`;
          }

          // Smooth transition overlay effect
          if (transitionOverlay) {
            const overlayOpacity = Math.sin(progress * Math.PI) * 0.3;
            const overlaySize = 30 + progress * 70;
            transitionOverlay.style.background = `radial-gradient(circle at 50% 50%,
              rgba(255, 255, 255, ${overlayOpacity}) 0%,
              rgba(255, 255, 255, ${overlayOpacity * 0.5}) ${overlaySize}%,
              rgba(255, 255, 255, 0) 100%)`;
            transitionOverlay.style.opacity = `${progress * 0.8}`;
          }

          // Smooth radial gradients transition
          if (radialGradient1Ref.current) {
            const opacity = 0.08 + (0.15 - 0.08) * progress;
            radialGradient1Ref.current.style.background = `rgba(186, 66, 255, ${opacity})`;
          }
          if (radialGradient2Ref.current) {
            const cyanOpacity = 0.08 * (1 - progress);
            const purpleOpacity = 0.12 * progress;
            radialGradient2Ref.current.style.background = `rgba(${progress > 0.5 ? '48, 44, 255' : '0, 225, 255'}, ${progress > 0.5 ? purpleOpacity : cyanOpacity})`;
          }
          if (radialGradient3Ref.current) {
            const purpleStart = 0.03 + (0.08 - 0.03) * progress;
            const purpleEnd = 0.03 + (0.06 - 0.03) * progress;
            radialGradient3Ref.current.style.background = `linear-gradient(45deg, rgba(48, 44, 255, ${purpleStart}) 0%, rgba(30, 26, 153, ${purpleEnd}) 100%)`;
          }

          // Smooth particle color transition
          const particles = container.querySelectorAll('.futuristic-particle');
          particles.forEach((particle, index) => {
            const originalColor = index % 2 === 0 ? '#302cff' : '#00E1FF';
            if (progress > 0.3) {
              (particle as HTMLElement).style.backgroundColor = '#302cff';
            } else {
              (particle as HTMLElement).style.backgroundColor = originalColor;
            }
          });

          // Smooth orbs color transition
          const orbs = container.querySelectorAll('.futuristic-orb');
          orbs.forEach((orb, index) => {
            if (progress > 0.4) {
              (orb as HTMLElement).style.backgroundColor = '#302cff';
            } else {
              if (index === 0) (orb as HTMLElement).style.backgroundColor = '#302cff';
              else if (index === 1) (orb as HTMLElement).style.backgroundColor = '#00E1FF';
              else (orb as HTMLElement).style.backgroundColor = '#FFFFFF';
            }
          });

          // Smooth circuit pattern transition
          const circuitPattern = container.querySelector('.circuit-pattern');
          if (circuitPattern) {
            if (progress > 0.5) {
              circuitPattern.setAttribute('fill', 'url(#circuit-white)');
            } else {
              circuitPattern.setAttribute('fill', 'url(#circuit)');
            }
          }
        }
      });

      // Add smooth exit transition
      ScrollTrigger.create({
        trigger: "#mobile-app-showcase",
        start: "bottom 95%",
        end: "bottom 0%",
        scrub: 1.5, // Smooth scrubbing for gradual exit transition
        onUpdate: (self) => {
          const progress = self.progress;

          // Smooth grid transition back to original
          if (gridRef.current) {
            const whiteOpacity = 0.1 * (1 - progress);
            const purpleOpacity = 0.1 * progress;
            gridRef.current.style.backgroundImage = `
              linear-gradient(rgba(255, 255, 255, ${whiteOpacity}) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255, 255, 255, ${whiteOpacity}) 1px, transparent 1px),
              linear-gradient(rgba(186, 66, 255, ${purpleOpacity}) 1px, transparent 1px),
              linear-gradient(90deg, rgba(186, 66, 255, ${purpleOpacity}) 1px, transparent 1px)
            `;
          }

          // Smooth background gradient transition back
          if (gradientRef.current) {
            const whiteR = 255 - (255 - 10) * progress;
            const whiteG = 255 - (255 - 10) * progress;
            const whiteB = 255 - (255 - 10) * progress;
            const midR = 248 - (248 - 26) * progress;
            const midG = 250 - (250 - 26) * progress;
            const midB = 252 - (252 - 46) * progress;
            const endR = 255 - (255 - 22) * progress;
            const endG = 255 - (255 - 33) * progress;
            const endB = 255 - (255 - 62) * progress;

            gradientRef.current.style.background = `linear-gradient(135deg,
              rgba(${whiteR}, ${whiteG}, ${whiteB}, ${0.95}) 0%,
              rgba(${midR}, ${midG}, ${midB}, ${0.9}) 50%,
              rgba(${endR}, ${endG}, ${endB}, ${0.95}) 100%)`;
          }

          // Smooth transition overlay effect (reverse)
          if (transitionOverlay) {
            const overlayOpacity = Math.sin((1 - progress) * Math.PI) * 0.3;
            const overlaySize = 100 - progress * 70;
            transitionOverlay.style.background = `radial-gradient(circle at 50% 50%,
              rgba(255, 255, 255, ${overlayOpacity}) 0%,
              rgba(255, 255, 255, ${overlayOpacity * 0.5}) ${overlaySize}%,
              rgba(255, 255, 255, 0) 100%)`;
            transitionOverlay.style.opacity = `${(1 - progress) * 0.8}`;
          }

          // Smooth radial gradients transition back
          if (radialGradient1Ref.current) {
            const opacity = 0.15 - (0.15 - 0.08) * progress;
            radialGradient1Ref.current.style.background = `rgba(186, 66, 255, ${opacity})`;
          }
          if (radialGradient2Ref.current) {
            const purpleOpacity = 0.12 * (1 - progress);
            const cyanOpacity = 0.08 * progress;
            radialGradient2Ref.current.style.background = `rgba(${progress > 0.5 ? '0, 225, 255' : '48, 44, 255'}, ${progress > 0.5 ? cyanOpacity : purpleOpacity})`;
          }
          if (radialGradient3Ref.current) {
            const purpleStart = 0.08 - (0.08 - 0.03) * progress;
            const purpleEnd = 0.06 - (0.06 - 0.03) * progress;
            radialGradient3Ref.current.style.background = `linear-gradient(45deg, rgba(48, 44, 255, ${purpleStart}) 0%, rgba(${progress > 0.5 ? '0, 225, 255' : '30, 26, 153'}, ${purpleEnd}) 100%)`;
          }

          // Smooth particle color transition back
          const particles = container.querySelectorAll('.futuristic-particle');
          particles.forEach((particle, index) => {
            const originalColor = index % 2 === 0 ? '#302cff' : '#00E1FF';
            if (progress > 0.7) {
              (particle as HTMLElement).style.backgroundColor = originalColor;
            } else {
              (particle as HTMLElement).style.backgroundColor = '#302cff';
            }
          });

          // Smooth orbs color transition back
          const orbs = container.querySelectorAll('.futuristic-orb');
          orbs.forEach((orb, index) => {
            if (progress > 0.6) {
              if (index === 0) (orb as HTMLElement).style.backgroundColor = '#302cff';
              else if (index === 1) (orb as HTMLElement).style.backgroundColor = '#00E1FF';
              else (orb as HTMLElement).style.backgroundColor = '#FFFFFF';
            } else {
              (orb as HTMLElement).style.backgroundColor = '#302cff';
            }
          });

          // Smooth circuit pattern transition back
          const circuitPattern = container.querySelector('.circuit-pattern');
          if (circuitPattern) {
            if (progress > 0.5) {
              circuitPattern.setAttribute('fill', 'url(#circuit)');
            } else {
              circuitPattern.setAttribute('fill', 'url(#circuit-white)');
            }
          }
        }
      });
    };

    createBackgroundThemeChange();

    return () => {
      // Cleanup all animations and ScrollTriggers
      animationsRef.current.forEach(animation => {
        animation.kill();
      });
      animationsRef.current = [];
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, [isVisible]);

  

  return (
    <div
      ref={containerRef}
      className="fixed inset-0 pointer-events-none z-0 overflow-hidden"
    >
      {/* Static Grid Background - No animation for better performance */}
      <div
        ref={gridRef}
        className="absolute inset-0 opacity-5"
        style={{
          backgroundImage: `
            linear-gradient(rgba(186, 66, 255, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(186, 66, 255, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: "50px 50px",
          transition: "background-image 0.8s ease",
        }}
      />

      {/* Gradient Overlays */}
      <div
        ref={gradientRef}
        className="absolute inset-0 opacity-95"
        style={{
          background: "linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%)",
          transition: "background 0.8s ease",
        }}
      />

      {/* Enhanced Radial Gradients */}
      {isVisible && (
        <>
          <div
            ref={radialGradient1Ref}
            className="absolute top-1/4 left-1/4 w-96 h-96 rounded-full blur-3xl animate-optimized-pulse"
            style={{
              background: "rgba(186, 66, 255, 0.08)",
              transition: "background 0.8s ease",
            }}
          />
          <div
            ref={radialGradient2Ref}
            className="absolute bottom-1/4 right-1/4 w-96 h-96 rounded-full blur-3xl animate-optimized-pulse"
            style={{
              background: "rgba(0, 225, 255, 0.08)",
              animationDelay: "1s",
              transition: "background 0.8s ease",
            }}
          />
          <div
            ref={radialGradient3Ref}
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] rounded-full blur-3xl animate-spin-slow"
            style={{
              background: "linear-gradient(45deg, rgba(186, 66, 255, 0.03) 0%, rgba(0, 225, 255, 0.03) 100%)",
              transition: "background 0.8s ease",
            }}
          />
        </>
      )}

      {/* Circuit Pattern */}
      {isVisible && (
        <div ref={circuitRef} className="absolute inset-0 opacity-5" style={{ transition: "opacity 0.8s ease" }}>
          <svg width="100%" height="100%" className="absolute inset-0">
            <defs>
              <pattern id="circuit" x="0" y="0" width="200" height="200" patternUnits="userSpaceOnUse">
                <path d="M 0 100 L 50 100 L 50 50 L 150 50 L 150 150 L 200 150"
                      stroke="#BA42FF"
                      strokeWidth="1"
                      fill="none"
                      opacity="0.3" />
                <circle cx="50" cy="100" r="2" fill="#BA42FF" opacity="0.4" />
                <circle cx="150" cy="50" r="2" fill="#BA42FF" opacity="0.4" />
                <circle cx="150" cy="150" r="2" fill="#BA42FF" opacity="0.4" />
              </pattern>
              <pattern id="circuit-white" x="0" y="0" width="200" height="200" patternUnits="userSpaceOnUse">
                <path d="M 0 100 L 50 100 L 50 50 L 150 50 L 150 150 L 200 150"
                      stroke="#BA42FF"
                      strokeWidth="1"
                      fill="none"
                      opacity="0.5" />
                <circle cx="50" cy="100" r="2" fill="#BA42FF" opacity="0.6" />
                <circle cx="150" cy="50" r="2" fill="#BA42FF" opacity="0.6" />
                <circle cx="150" cy="150" r="2" fill="#BA42FF" opacity="0.6" />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#circuit)" className="circuit-pattern" style={{ transition: "fill 0.8s ease" }} />
          </svg>
        </div>
      )}

      {/* Floating Orbs */}
      {isVisible && (
        <>
          <div className="futuristic-orb absolute top-20 left-20 w-3 h-3 bg-[#BA42FF] rounded-full opacity-30 animate-bounce" style={{ animationDuration: "3s", transition: "background-color 0.8s ease" }} />
          <div className="futuristic-orb absolute top-40 right-32 w-2 h-2 bg-[#00E1FF] rounded-full opacity-40 animate-bounce" style={{ animationDuration: "4s", animationDelay: "1s", transition: "background-color 0.8s ease" }} />
          <div className="futuristic-orb absolute bottom-32 left-1/3 w-1 h-1 bg-white rounded-full opacity-20 animate-bounce" style={{ animationDuration: "5s", animationDelay: "2s", transition: "background-color 0.8s ease" }} />
        </>
      )}
    </div>
  );
}
