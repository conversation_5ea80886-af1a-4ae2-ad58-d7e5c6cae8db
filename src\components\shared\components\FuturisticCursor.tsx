"use client";

import { useEffect, useRef, useState, useCallback } from "react";

export default function FuturisticCursor() {
  const cursorRef = useRef<HTMLDivElement>(null);
  const cursorDotRef = useRef<HTMLDivElement>(null);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Check if mobile device
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768 || 'ontouchstart' in window);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    const cursor = cursorRef.current;
    const cursorDot = cursorDotRef.current;

    if (!cursor || !cursorDot) return;

    const { clientX: x, clientY: y } = e;

    // ULTRA-OPTIMIZED: Direct transform without RAF for better performance
    cursor.style.transform = `translate3d(${x - 20}px, ${y - 20}px, 0)`;

    // Simplified dot movement - no delay for better performance
    cursorDot.style.transform = `translate3d(${x - 4}px, ${y - 4}px, 0)`;
  }, []);

  useEffect(() => {
    if (isMobile) return; // Don't show cursor on mobile

    const cursor = cursorRef.current;
    const cursorDot = cursorDotRef.current;

    if (!cursor || !cursorDot) return;

    // Hide default cursor
    document.body.style.cursor = "none";

    document.addEventListener("mousemove", handleMouseMove, { passive: true });

    return () => {
      document.body.style.cursor = "auto";
      document.removeEventListener("mousemove", handleMouseMove);
    };
  }, [isMobile, handleMouseMove]);

  if (isMobile) return null; // Don't render on mobile

  return (
    <>
      {/* Main Cursor - Simple */}
      <div
        ref={cursorRef}
        className="futuristic-cursor fixed pointer-events-none"
        style={{
          width: '40px',
          height: '40px',
          willChange: "transform",
          zIndex: 999999,
          position: 'fixed',
          top: 0,
          left: 0
        }}
      >
        <div className="relative w-full h-full">
          {/* Outer ring */}
          <div className="absolute inset-0 border-2 rounded-full border-white/60"></div>

          {/* Inner ring */}
          <div className="absolute inset-1 border rounded-full border-[#BA42FF]/40"></div>

          {/* Center dot */}
          <div className="absolute top-1/2 left-1/2 w-1.5 h-1.5 rounded-full transform -translate-x-1/2 -translate-y-1/2 bg-[#BA42FF]"></div>
        </div>
      </div>

      {/* Cursor Dot - Following Dot */}
      <div
        ref={cursorDotRef}
        className="futuristic-cursor fixed rounded-full pointer-events-none bg-[#00E1FF]/80 transition-transform duration-200 ease-out"
        style={{
          width: '8px',
          height: '8px',
          willChange: "transform",
          zIndex: 999998,
          position: 'fixed',
          top: 0,
          left: 0
        }}
      ></div>
    </>
  );
}
