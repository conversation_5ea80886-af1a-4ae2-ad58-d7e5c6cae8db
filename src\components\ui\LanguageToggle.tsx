import { Language } from "@/types/policy";

interface LanguageToggleProps {
  language: Language;
  onLanguageChange: (language: Language) => void;
}

export default function LanguageToggle({ language, onLanguageChange }: LanguageToggleProps) {
  return (
    <div className="flex justify-center mb-8">
      <div className="bg-white/10 backdrop-blur-sm rounded-lg p-1 border border-white/20">
        <button
          onClick={() => onLanguageChange('en')}
          className={`px-4 py-2 rounded-md font-semibold transition-all duration-300 ${
            language === 'en'
              ? 'bg-[#302cff] text-white'
              : 'text-gray-300 hover:text-white'
          }`}
        >
          English
        </button>
        <button
          onClick={() => onLanguageChange('ar')}
          className={`px-4 py-2 rounded-md font-semibold transition-all duration-300 ${
            language === 'ar'
              ? 'bg-[#302cff] text-white'
              : 'text-gray-300 hover:text-white'
          }`}
        >
          العربية
        </button>
      </div>
    </div>
  );
}
