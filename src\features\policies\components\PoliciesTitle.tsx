interface PoliciesTitleProps {
  titleRef: React.RefObject<HTMLDivElement>;
}

export default function PoliciesTitle({ titleRef }: PoliciesTitleProps) {
  return (
    <div ref={titleRef} className="text-center mb-16">
      <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 font-orbitron leading-tight">
        Our <span className="text-[#302cff]">Policies</span>
      </h1>
      <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto font-sora leading-relaxed">
        Transparency and trust through clear policies and guidelines
      </p>
    </div>
  );
}
