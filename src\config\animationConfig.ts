/**
 * Animation Configuration
 * 
 * Centralized configuration for all GSAP ScrollTrigger animations
 * Edit these values to fine-tune animation behavior across the entire app
 */

export const ANIMATION_CONFIG = {
  // ===========================================
  // SCROLL TRIGGER SETTINGS
  // ===========================================
  
  // Scrub values (how animations follow scroll speed)
  // Lower values = faster animation response
  // Higher values = slower, more smooth animation response
  SCRUB: {
    // Services Section
    SERVICES_IMAGES: 0.5,        // Main service images (1:1 with scroll)
    SERVICES_TEXT: 0.5,          // Service text content (2x faster than scroll)
    SERVICES_FEATURES: 0.5,      // Feature tags
    SERVICES_BUTTONS: 0.5,       // CTA buttons
    
    // Mobile App Showcase
    MOBILE_MAIN_SCREENSHOT: 0.5,     // Main app screenshot
    MOBILE_ADDITIONAL_SCREENS: 0.5,  // Additional screenshots
    MOBILE_FEATURE_CARDS: 0.5,       // Feature cards (quick response)
    MOBILE_TRAVEL_OPTIONS: 0.4,      // Travel option cards
    MOBILE_TEXT_CONTENT: 0.5,        // Text content
    
    // Landing Section
    LANDING_ELEMENTS: 0.6,       // Landing page elements
    
    // Partner Section
    PARTNER_CONTENT: 0.7,        // Partner section content
    
    // Get In Touch
    CONTACT_ELEMENTS: 0.5,       // Contact form elements

    // Complete Solutions Section
    COMPLETE_SOLUTIONS_TITLE: 0.3,     // Fast title animations
    COMPLETE_SOLUTIONS_DESCRIPTION: 0.4, // Smooth description animations
    COMPLETE_SOLUTIONS_CARDS: 0.5,     // Smooth card animations

    // Mobile Showcase Additional Sections
    MOBILE_SHOWCASE_SCREENSHOTS: 0.6,  // Smooth screenshot animations
    MOBILE_SHOWCASE_TEXT: 0.3,         // Fast text animations
    MOBILE_SHOWCASE_CARDS: 0.4,        // Smooth card animations
  },

  // ===========================================
  // TRIGGER POINTS (when animations start/end)
  // ===========================================
  
  TRIGGERS: {
    // Services Section - EARLIER TIMING (animations start much sooner)
    SERVICES: {
      IMAGES: { start: "top 95%", end: "bottom 80%" },     // Start when element is 95% from top (much earlier)
      TEXT: { start: "top 90%", end: "bottom 80%" },      // Start when element is 90% from top
      FEATURES: { start: "top 85%", end: "bottom 85%" },  // Start when element is 85% from top
      BUTTONS: { start: "top 80%", end: "bottom 80%" },   // Start when element is 80% from top
    },

    // Mobile App Showcase - MUCH EARLIER TIMING (animations start immediately and end quickly)
    MOBILE: {
      MAIN_SCREENSHOT: { start: "top 120%", end: "bottom 80%" },     // Start before element enters viewport, end quickly
      ADDITIONAL_SCREENS: { start: "top 110%", end: "bottom 90%" },  // Start very early, end quickly
      FEATURE_CARDS: { start: "top 100%", end: "bottom 80%" },       // Start when element just enters viewport
      TRAVEL_OPTIONS: { start: "top 100%", end: "bottom 80%" },      // Start early, end quickly
      TEXT_CONTENT: { start: "top 100%", end: "bottom 80%" },        // Start early, end quickly
    },

    // Landing Section - EARLIER TIMING
    LANDING: {
      ELEMENTS: { start: "top 95%", end: "bottom 5%" },
    },

    // Partner Section - EARLIER TIMING
    PARTNER: {
      CONTENT: { start: "top 95%", end: "bottom 5%" },
    },

    // Contact Section - EARLIER TIMING
    CONTACT: {
      ELEMENTS: { start: "top 90%", end: "bottom 10%" },
    },

    // Complete Solutions Section - EARLIER TIMING (similar to Services)
    COMPLETE_SOLUTIONS: {
      TITLE: { start: "top 95%", end: "bottom 75%" },
      DESCRIPTION: { start: "top 90%", end: "bottom 70%" },
      CARDS: { start: "top 85%", end: "bottom 65%" },
    },

    // Mobile App Showcase - Additional sections - MUCH EARLIER TIMING
    MOBILE_SHOWCASE: {
      SCREENSHOTS: { start: "top 120%", end: "bottom 80%" },        // Start before viewport, end quickly
      WHY_CHOOSE_TITLE: { start: "top 110%", end: "bottom 90%" },   // Start very early, end quickly
      WHY_CHOOSE_DESCRIPTION: { start: "top 105%", end: "bottom 85%" }, // Start early, end quickly
      WHY_CHOOSE_CARDS: { start: "top 100%", end: "bottom 80%" },   // Start when entering viewport, end quickly
      TRAVEL_WAY_TITLE: { start: "top 110%", end: "bottom 90%" },   // Start very early, end quickly
      TRAVEL_WAY_DESCRIPTION: { start: "top 105%", end: "bottom 85%" }, // Start early, end quickly
      TRAVEL_WAY_CARDS: { start: "top 100%", end: "bottom 80%" },   // Start when entering viewport, end quickly
    },
  },

  // ===========================================
  // ANIMATION DURATIONS
  // ===========================================
  
  DURATIONS: {
    // Standard durations for different element types
    FAST: 0.3,           // Quick animations (buttons, small elements)
    NORMAL: 0.6,         // Standard animations (text, cards)
    SLOW: 1.0,           // Slow animations (large images, main elements)
    VERY_SLOW: 1.5,      // Very slow animations (hero elements)
  },

  // ===========================================
  // EASING FUNCTIONS
  // ===========================================
  
  EASING: {
    // GSAP easing presets
    SMOOTH: "power2.out",        // Smooth, natural easing
    BOUNCY: "back.out(1.7)",     // Bouncy entrance effect
    ELASTIC: "elastic.out(1, 0.3)", // Elastic effect
    QUICK: "power1.out",         // Quick, snappy easing
    SLOW: "power3.out",          // Slow, smooth easing
  },

  // ===========================================
  // STAGGER SETTINGS
  // ===========================================
  
  STAGGER: {
    // Delays between multiple elements animating
    FEATURE_CARDS: 0.1,          // Delay between feature cards
    SCREENSHOTS: 0.1,            // Delay between screenshots
    TEXT_ELEMENTS: 0.2,          // Delay between text elements
    BUTTONS: 0.3,                // Delay between buttons
  },

  // ===========================================
  // INITIAL STATES
  // ===========================================
  
  INITIAL_STATES: {
    // Starting positions/properties for elements
    IMAGES: {
      opacity: 0,
      scale: 0.5,
      rotationY: 45,
      y: 100,
    },
    
    TEXT: {
      opacity: 0,
      y: 30,
      scale: 0.9,
    },
    
    CARDS: {
      opacity: 0,
      y: 40,
      scale: 0.95,
    },
    
    BUTTONS: {
      opacity: 0,
      scale: 0.8,
      y: 20,
    },
    
    SCREENSHOTS: {
      MAIN: {
        opacity: 0,
        y: 80,
        scale: 0.7,
        rotationY: 20,
      },
      
      ADDITIONAL: {
        opacity: 0,
        x: -60,
        y: 60,
        scale: 0.6,
        rotation: -8,
      },
    },
  },

  // ===========================================
  // DEVELOPMENT SETTINGS
  // ===========================================
  
  DEBUG: {
    // Enable/disable debug logging
    ENABLED: process.env.NODE_ENV === 'development',
 
    // Show ScrollTrigger markers (for debugging)
    SHOW_MARKERS: false,

    // Log animation events
    LOG_EVENTS: true,

    // Show Lottie scroll control debug
    SHOW_LOTTIE_DEBUG: true,
  },

  // ===========================================
  // PERFORMANCE SETTINGS
  // ===========================================
  
  PERFORMANCE: {
    // Fallback timeout (ms) - make elements visible if animations fail
    FALLBACK_TIMEOUT: 2000, // Reasonable fallback time

    // Loading check interval (ms)
    LOADING_CHECK_INTERVAL: 100, // Standard check interval

    // Element check retry interval (ms)
    ELEMENT_CHECK_INTERVAL: 100, // Standard check interval
  },
};

// ===========================================
// HELPER FUNCTIONS
// ===========================================

/**
 * Get scrub value for a specific animation type
 */
export const getScrubValue = (section: keyof typeof ANIMATION_CONFIG.SCRUB, element?: string) => {
  if (element && typeof ANIMATION_CONFIG.SCRUB[section] === 'object') {
    return (ANIMATION_CONFIG.SCRUB[section] as Record<string, number>)[element] || 0.5;
  }
  return ANIMATION_CONFIG.SCRUB[section] || 0.5;
};

/**
 * Get trigger points for a specific animation
 */
export const getTriggerPoints = (section: string, element: string) => {
  const triggers = (ANIMATION_CONFIG.TRIGGERS as Record<string, Record<string, { start: string; end: string }>>)[section];
  if (triggers && triggers[element]) {
    return triggers[element];
  }
  return { start: "top 95%", end: "bottom 5%" }; // Default to earlier timing
};

/**
 * Log debug information (only in development)
 */
export const debugLog = (message: string, data?: unknown) => {
  if (ANIMATION_CONFIG.DEBUG.ENABLED && ANIMATION_CONFIG.DEBUG.LOG_EVENTS) {
    console.log(`🎬 Animation Debug: ${message}`, data || '');
  }
};

export default ANIMATION_CONFIG;
