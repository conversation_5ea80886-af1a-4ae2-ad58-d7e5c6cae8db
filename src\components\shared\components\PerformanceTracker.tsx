"use client";

import { useState } from "react";
import { usePerformanceTracker } from "@/hooks/usePerformanceTracker";

export default function PerformanceTracker() {
  const [isVisible, setIsVisible] = useState(false);
  const { 
    metrics, 
    report, 
    isTracking, 
    startTracking, 
    stopTracking, 
    clearData, 
    currentFPS 
  } = usePerformanceTracker(true);

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 z-50 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg shadow-lg transition-colors"
      >
        📊 Performance
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-black/90 backdrop-blur-sm text-white p-4 rounded-lg shadow-xl max-w-md w-full max-h-96 overflow-y-auto">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold text-purple-400">Performance Tracker</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-white"
        >
          ✕
        </button>
      </div>

      {/* Current FPS */}
      <div className="mb-4">
        <div className="text-sm text-gray-300">Current FPS</div>
        <div className={`text-2xl font-bold ${
          currentFPS >= 50 ? 'text-green-400' : 
          currentFPS >= 30 ? 'text-yellow-400' : 'text-red-400'
        }`}>
          {currentFPS}
        </div>
      </div>

      {/* Controls */}
      <div className="flex gap-2 mb-4">
        <button
          onClick={isTracking ? stopTracking : startTracking}
          className={`px-3 py-1 rounded text-sm font-medium ${
            isTracking 
              ? 'bg-red-600 hover:bg-red-700' 
              : 'bg-green-600 hover:bg-green-700'
          }`}
        >
          {isTracking ? '⏹️ Stop' : '▶️ Start'}
        </button>
        <button
          onClick={clearData}
          className="px-3 py-1 rounded text-sm font-medium bg-gray-600 hover:bg-gray-700"
        >
          🗑️ Clear
        </button>
      </div>

      {/* Tracking Status */}
      {isTracking && (
        <div className="mb-4 p-2 bg-blue-900/50 rounded">
          <div className="text-sm text-blue-300">
            📈 Tracking... ({metrics.length} samples)
          </div>
        </div>
      )}

      {/* Performance Report */}
      {report && (
        <div className="space-y-3">
          <div className="border-t border-gray-700 pt-3">
            <h4 className="font-semibold text-purple-300 mb-2">Performance Report</h4>
            
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="text-gray-400">Avg FPS:</span>
                <span className={`ml-1 font-medium ${
                  report.averageFPS >= 50 ? 'text-green-400' : 
                  report.averageFPS >= 30 ? 'text-yellow-400' : 'text-red-400'
                }`}>
                  {report.averageFPS}
                </span>
              </div>
              <div>
                <span className="text-gray-400">Min FPS:</span>
                <span className="ml-1 font-medium text-red-300">{report.minFPS}</span>
              </div>
              <div>
                <span className="text-gray-400">Max FPS:</span>
                <span className="ml-1 font-medium text-green-300">{report.maxFPS}</span>
              </div>
              <div>
                <span className="text-gray-400">Scroll/s:</span>
                <span className="ml-1 font-medium">{report.scrollEventsPerSecond}</span>
              </div>
              <div>
                <span className="text-gray-400">Animations:</span>
                <span className="ml-1 font-medium">{report.activeAnimations}</span>
              </div>
              <div>
                <span className="text-gray-400">Memory:</span>
                <span className={`ml-1 font-medium ${
                  report.memoryTrend === 'increasing' ? 'text-red-400' :
                  report.memoryTrend === 'decreasing' ? 'text-green-400' : 'text-yellow-400'
                }`}>
                  {report.memoryTrend}
                </span>
              </div>
            </div>
          </div>

          {/* Bottlenecks */}
          {report.bottlenecks.length > 0 && (
            <div className="border-t border-gray-700 pt-3">
              <h5 className="font-semibold text-red-300 mb-2">🚨 Bottlenecks</h5>
              <ul className="text-sm space-y-1">
                {report.bottlenecks.map((bottleneck, index) => (
                  <li key={index} className="text-red-200">
                    • {bottleneck}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Recommendations */}
          {report.recommendations.length > 0 && (
            <div className="border-t border-gray-700 pt-3">
              <h5 className="font-semibold text-green-300 mb-2">💡 Recommendations</h5>
              <ul className="text-sm space-y-1">
                {report.recommendations.map((rec, index) => (
                  <li key={index} className="text-green-200">
                    • {rec}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Instructions */}
      {!isTracking && !report && (
        <div className="text-sm text-gray-400">
          Click &quot;Start&quot; to begin tracking performance metrics. Scroll around and interact with the page, then click &quot;Stop&quot; to see the analysis.
        </div>
      )}
    </div>
  );
}
