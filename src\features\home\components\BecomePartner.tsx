"use client";

import Image from "next/image";
import { useBecomePartner } from "../hooks/useBecomePartner";
export default function BecomePartner() {
  const {
    refs: { sectionRef, titleRef, subtitleRef, advantagesRef }
  } = useBecomePartner();




  return (
    <section ref={sectionRef} id="become-partner" className="min-h-screen py-20 px-4 lg:px-8 relative z-10">
      {/* Header */}
      <div className="text-center mb-20">
        <div ref={titleRef}>
          <h2 className="partner-title text-5xl md:text-6xl lg:text-7xl font-black text-white font-orbitron leading-tight mb-6">
            Become Our <span className="bg-gradient-to-r from-[#4d42ff] via-[#302cff] to-[#1e1a99] bg-clip-text text-transparent">Partner</span>
          </h2>
          <div className="partner-line w-32 h-1 bg-gradient-to-r from-[#302cff] to-[#1e1a99] mx-auto rounded-full"></div>
        </div>

        <p ref={subtitleRef} className="partner-subtitle text-xl md:text-2xl text-gray-200 max-w-4xl mx-auto font-sora leading-relaxed mt-8">
          Join our growing network of transportation partners and unlock new opportunities for your business
        </p>
      </div>

      {/* Main Content - Center Image with Surrounding Cards */}
      <div ref={advantagesRef} className="max-w-6xl mx-auto mb-20">
        {/* Mobile Layout - Image at top, cards below */}
        <div className="block md:hidden">
          {/* Image at top for mobile */}
          <div className="flex items-center justify-center mb-12">
            <div className="w-full max-w-[300px]">
              <Image
                src="/assets/become_partner/partner.png"
                alt="Handshake"
                width={300}
                height={300}
                className="partner-image object-contain"
              />
            </div>
          </div>

          {/* Cards in 2x2 grid for mobile */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            {/* Priority Placement */}
            <div className="partner-benefit-item p-4 rounded-lg flex flex-col items-center justify-center text-center">
              <div className="mb-4">
                <div className="w-20 h-20 rounded-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center text-4xl">
                  🎯
                </div>
              </div>
              <h4 className="text-2xl font-bold text-red-400 mb-4 font-orbitron">
                Priority Placement
              </h4>
              <p className="text-base text-gray-300 font-sora leading-relaxed">
                Featured placement and enhanced visibility
              </p>
            </div>

            {/* Technology Integration */}
            <div className="partner-benefit-item p-4 rounded-lg flex flex-col items-center justify-center text-center">
              <div className="mb-4">
                <div className="w-20 h-20 rounded-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center text-4xl">
                  ⚡
                </div>
              </div>
              <h4 className="text-2xl font-bold text-yellow-400 mb-4 font-orbitron">
                Technology Integration
              </h4>
              <p className="text-base text-gray-300 font-sora leading-relaxed">
                Seamless platform integration with API access
              </p>
            </div>

            {/* Revenue Growth */}
            <div className="partner-benefit-item p-4 rounded-lg flex flex-col items-center justify-center text-center">
              <div className="mb-4">
                <div className="w-20 h-20 rounded-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center text-4xl">
                  💰
                </div>
              </div>
              <h4 className="text-2xl font-bold text-green-400 mb-4 font-orbitron">
                Revenue Growth
              </h4>
              <p className="text-base text-gray-300 font-sora leading-relaxed">
                Commission-based partnership model with exclusive deals
              </p>
            </div>

            {/* Market Expansion */}
            <div className="partner-benefit-item p-4 rounded-lg flex flex-col items-center justify-center text-center">
              <div className="mb-4">
                <div className="w-20 h-20 rounded-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center text-4xl">
                  🌐
                </div>
              </div>
              <h4 className="text-2xl font-bold text-blue-400 mb-4 font-orbitron">
                Market Expansion
              </h4>
              <p className="text-base text-gray-300 font-sora leading-relaxed">
                Reach new customers across Egypt and beyond
              </p>
            </div>
          </div>
        </div>

        {/* Desktop Layout - Original 3-column layout */}
        <div className="hidden md:block">
          <div className="relative grid grid-cols-3 gap-x-2 items-stretch min-h-[600px]">
          {/* Priority Placement - Column 1 */}
          <div className="flex flex-col justify-between h-full">
            <div className="partner-benefit-item p-4 rounded-lg flex flex-col items-center justify-center text-center">
              <div className="mb-4">
                <div className="w-20 h-20 rounded-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center text-4xl">
                  🎯
                </div>
              </div>
              <h4 className="text-2xl font-bold text-red-400 mb-4 font-orbitron">
                Priority Placement
              </h4>
              <p className="text-base text-gray-300 font-sora leading-relaxed">
                Featured placement and enhanced visibility
              </p>
            </div>
            <div className="partner-benefit-item p-4 rounded-lg flex flex-col items-center justify-center text-center">
              <div className="mb-4">
                <div className="w-20 h-20 rounded-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center text-4xl">
                  ⚡
                </div>
              </div>
              <h4 className="text-2xl font-bold text-yellow-400 mb-4 font-orbitron">
                Technology Integration
              </h4>
              <p className="text-base text-gray-300 font-sora leading-relaxed">
                Seamless platform integration with API access
              </p>
            </div>
          </div>

          {/* Center Column - Image Only */}
          <div className="flex items-center justify-center h-full">
            <div className="w-full max-w-[450px]">
              <Image
                src="/assets/become_partner/partner.png"
                alt="Handshake"
                width={450}
                height={450}
                className="partner-image object-contain"
              />
            </div>
          </div>

          {/* Revenue Growth - Column 3 */}
          <div className="flex flex-col justify-between h-full">
            <div className="partner-benefit-item p-4 rounded-lg flex flex-col items-center justify-center text-center">
              <div className="mb-4">
                <div className="w-20 h-20 rounded-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center text-4xl">
                  💰
                </div>
              </div>
              <h4 className="text-2xl font-bold text-green-400 mb-4 font-orbitron">
                Revenue Growth
              </h4>
              <p className="text-base text-gray-300 font-sora leading-relaxed">
                Commission-based partnership model with exclusive deals
              </p>
            </div>
            <div className="partner-benefit-item p-4 rounded-lg flex flex-col items-center justify-center text-center">
              <div className="mb-4">
                <div className="w-20 h-20 rounded-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center text-4xl">
                  🌐
                </div>
              </div>
              <h4 className="text-2xl font-bold text-blue-400 mb-4 font-orbitron">
                Market Expansion
              </h4>
              <p className="text-base text-gray-300 font-sora leading-relaxed">
                Reach new customers across Egypt and beyond
              </p>
            </div>
          </div>
        </div>
        </div>
      </div>

      {/* Partner Application Form */}
      {/* <div ref={formRef} className="partner-element">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold text-white mb-4 font-orbitron">
              Partner Application Form
            </h3>
            <p className="text-lg text-gray-300 font-sora">
              Fill out the form below to start your partnership journey with us
            </p>
          </div>

          <div className="bg-gradient-to-br from-slate-50/95 via-blue-50/90 to-indigo-50/95 backdrop-blur-xl border border-blue-200/50 rounded-2xl p-8 md:p-12 shadow-2xl shadow-blue-900/20">
            <form onSubmit={handleSubmit} className="space-y-6">

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="companyName" className="block text-sm font-medium text-white mb-2">
                    Company Name *
                  </label>
                  <input
                    type="text"
                    id="companyName"
                    name="companyName"
                    value={formData.companyName}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-blue-100 border border-blue-300 rounded-lg text-slate-800 placeholder-white focus:outline-none focus:ring-2 focus:ring-[#302cff] focus:border-[#302cff] focus:bg-blue-200 transition-all duration-300"
                    placeholder="Enter your company name"
                  />
                </div>
                <div>
                  <label htmlFor="contactPerson" className="block text-sm font-medium text-white mb-2">
                    Contact Person *
                  </label>
                  <input
                    type="text"
                    id="contactPerson"
                    name="contactPerson"
                    value={formData.contactPerson}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-blue-100 border border-blue-300 rounded-lg text-slate-800 placeholder-white focus:outline-none focus:ring-2 focus:ring-[#302cff] focus:border-[#302cff] focus:bg-blue-200 transition-all duration-300"
                    placeholder="Enter contact person name"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-slate-700 mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-white/80 border border-blue-200 rounded-lg text-slate-800 placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-[#302cff] focus:border-[#302cff] focus:bg-white transition-all duration-300"
                    placeholder="Enter your email address"
                  />
                </div>
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-slate-700 mb-2">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-white/80 border border-blue-200 rounded-lg text-slate-800 placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-[#302cff] focus:border-[#302cff] focus:bg-white transition-all duration-300"
                    placeholder="Enter your phone number"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="businessType" className="block text-sm font-medium text-slate-700 mb-2">
                    Business Type *
                  </label>
                  <select
                    id="businessType"
                    name="businessType"
                    value={formData.businessType}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-white/80 border border-blue-200 rounded-lg text-slate-800 focus:outline-none focus:ring-2 focus:ring-[#302cff] focus:border-[#302cff] focus:bg-white transition-all duration-300"
                  >
                    <option value="">Select business type</option>
                    <option value="bus-company">Bus Company</option>
                    <option value="taxi-service">Taxi Service</option>
                    <option value="car-rental">Car Rental</option>
                    <option value="logistics">Logistics & Freight</option>
                    <option value="travel-agency">Travel Agency</option>
                    <option value="other">Other</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="location" className="block text-sm font-medium text-slate-700 mb-2">
                    Location *
                  </label>
                  <input
                    type="text"
                    id="location"
                    name="location"
                    value={formData.location}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-white/80 border border-blue-200 rounded-lg text-slate-800 placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-[#302cff] focus:border-[#302cff] focus:bg-white transition-all duration-300"
                    placeholder="Enter your location/city"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="fleetSize" className="block text-sm font-medium text-slate-700 mb-2">
                    Fleet Size
                  </label>
                  <select
                    id="fleetSize"
                    name="fleetSize"
                    value={formData.fleetSize}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 bg-white/80 border border-blue-200 rounded-lg text-slate-800 focus:outline-none focus:ring-2 focus:ring-[#302cff] focus:border-[#302cff] focus:bg-white transition-all duration-300"
                  >
                    <option value="">Select fleet size</option>
                    <option value="1-5">1-5 vehicles</option>
                    <option value="6-20">6-20 vehicles</option>
                    <option value="21-50">21-50 vehicles</option>
                    <option value="51-100">51-100 vehicles</option>
                    <option value="100+">100+ vehicles</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="experience" className="block text-sm font-medium text-slate-700 mb-2">
                    Years of Experience
                  </label>
                  <select
                    id="experience"
                    name="experience"
                    value={formData.experience}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 bg-white/80 border border-blue-200 rounded-lg text-slate-800 focus:outline-none focus:ring-2 focus:ring-[#302cff] focus:border-[#302cff] focus:bg-white transition-all duration-300"
                  >
                    <option value="">Select experience</option>
                    <option value="0-2">0-2 years</option>
                    <option value="3-5">3-5 years</option>
                    <option value="6-10">6-10 years</option>
                    <option value="11-20">11-20 years</option>
                    <option value="20+">20+ years</option>
                  </select>
                </div>
              </div>

              <div>
                <label htmlFor="message" className="block text-sm font-medium text-slate-700 mb-2">
                  Additional Information
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  rows={4}
                  className="w-full px-4 py-3 bg-white/80 border border-blue-200 rounded-lg text-slate-800 placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-[#302cff] focus:border-[#302cff] focus:bg-white transition-all duration-300 resize-none"
                  placeholder="Tell us more about your business and why you'd like to partner with us..."
                />
              </div>

              <div className="text-center pt-4">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-12 py-4 bg-[#302cff] text-white font-semibold rounded-lg hover:bg-[#1e1a99] focus:outline-none focus:ring-2 focus:ring-[#302cff] focus:ring-offset-2 focus:ring-offset-blue-50 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none text-lg shadow-lg shadow-blue-500/25"
                >
                  {isSubmitting ? (
                    <span className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Submitting...
                    </span>
                  ) : (
                    'Submit Application'
                  )}
                </button>
              </div>
              {submitStatus === 'success' && (
                <div className="text-center p-4 bg-green-500/20 border border-green-500/30 rounded-xl">
                  <p className="text-green-300 font-semibold font-sora">
                    ✅ Application submitted successfully! We'll get back to you within 24 hours.
                  </p>
                </div>
              )}

              {submitStatus === 'error' && (
                <div className="text-center p-4 bg-red-500/20 border border-red-500/30 rounded-xl">
                  <p className="text-red-300 font-semibold font-sora">
                    ❌ There was an error submitting your application. Please try again.
                  </p>
                </div>
              )}
            </form>
          </div>
        </div>
      </div>
       */}
    </section>
  );
}
