interface ErrorDisplayProps {
  error: string;
  onRetry?: () => void;
  title?: string;
}

export default function ErrorDisplay({ 
  error, 
  onRetry, 
  title = "Something went wrong" 
}: ErrorDisplayProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0a0a0a] via-[#1a1a2e] to-[#16213e] flex items-center justify-center">
      <div className="text-center max-w-md mx-auto px-6">
        <div className="text-red-400 text-6xl mb-4">⚠️</div>
        <h2 className="text-2xl font-bold text-white mb-4 font-orbitron">{title}</h2>
        <p className="text-gray-300 mb-6 font-sora">{error}</p>
        {onRetry && (
          <button
            onClick={onRetry}
            className="bg-[#302cff] hover:bg-[#1e1a99] text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-300 hover:scale-105 transform"
          >
            Try Again
          </button>
        )}
      </div>
    </div>
  );
}
