"use client";

import { useRef, useEffect } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

export const useAboutUs = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLDivElement>(null);
  const logoRef = useRef<HTMLDivElement>(null);
  const visionRef = useRef<HTMLDivElement>(null);
  const missionRef = useRef<HTMLDivElement>(null);
  const valuesRef = useRef<HTMLDivElement>(null);
  const contactRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const ctx = gsap.context(() => {
      // Initial setup - hide all elements
      gsap.set([titleRef.current, logoRef.current, visionRef.current, missionRef.current, valuesRef.current, contactRef.current], {
        opacity: 0,
        y: 100,
        scale: 0.8
      });

      // Create timeline for entrance animations
      const tl = gsap.timeline();

      // Title entrance
      tl.to(titleRef.current, {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 1.2,
        ease: "power3.out"
      })
      // Logo entrance with rotation
      .to(logoRef.current, {
        opacity: 1,
        y: 0,
        scale: 1,
        rotation: 360,
        duration: 1.5,
        ease: "back.out(1.7)"
      }, "-=0.8")
      // Vision section entrance
      .to(visionRef.current, {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 1,
        ease: "power2.out"
      }, "-=0.6")
      // Mission section entrance
      .to(missionRef.current, {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 1,
        ease: "power2.out"
      }, "-=0.8")
      // Values section entrance
      .to(valuesRef.current, {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 1,
        ease: "power2.out"
      }, "-=0.6")
      // Contact section entrance
      .to(contactRef.current, {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 1,
        ease: "power2.out"
      }, "-=0.4");

    }, containerRef);

    return () => ctx.revert();
  }, []);

  return {
    refs: {
      containerRef,
      titleRef,
      logoRef,
      visionRef,
      missionRef,
      valuesRef,
      contactRef
    }
  };
};
