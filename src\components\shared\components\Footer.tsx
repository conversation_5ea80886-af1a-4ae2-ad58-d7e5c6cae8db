"use client";

import Link from "next/link";
import Image from "next/image";
import ContactButton from "./ContactButton";

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gradient-to-br from-gray-900 via-gray-800 to-black border-t border-[#302cff]/20 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-gradient-to-r from-[#302cff]/10 to-blue-500/10"></div>
        <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_50%_50%,rgba(48,44,255,0.1),transparent_50%)]"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-8 lg:px-16 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">

          {/* LEFT - Company Logo & Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-gradient-to-br from-[#302cff] to-[#1e1a99] rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-lg font-orbitron">T</span>
              </div>
              <span className="text-xl font-bold text-white font-orbitron">TregoTech</span>
            </div>
            <p className="text-gray-300 font-sora text-sm leading-relaxed">
              Transforming ideas into digital reality with cutting-edge technology solutions.
            </p>
            <ContactButton variant="outline" size="sm">
              Get In Touch
            </ContactButton>
          </div>

          {/* MIDDLE - Contact Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-bold text-white font-orbitron mb-4">Contact Info</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <span className="text-[#302cff] text-sm">📧</span>
                <span className="text-gray-300 font-sora text-sm"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3">
                <span className="text-[#302cff] text-sm">📱</span>
                <span className="text-gray-300 font-sora text-sm">+201112628619</span>
              </div>
            </div>
          </div>

          {/* RIGHT - Trego App & Download */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 mb-4">
              <Image
                src="/assets/trego_app/logo.png"
                alt="Trego App Logo"
                width={24}
                height={24}
                priority
                className="rounded-lg"
              />
              <h3 className="text-lg font-bold text-white font-orbitron">Trego App</h3>
            </div>
            <p className="text-gray-300 font-sora text-sm mb-4">
              Your ultimate travel companion for Egypt.
            </p>

            {/* Download Buttons - Side by Side */}
            <div className="flex space-x-2 mb-3">
              <a
                href="https://apps.apple.com/eg/app/trego/id6742484811"
                target="_blank"
                rel="noopener noreferrer"
                className="flex-1 rounded-lg overflow-hidden hover:opacity-80 transition-opacity duration-200 group"
              >
                <Image
                  src="/assets/trego_app/appStore.webp"
                  alt="Download on App Store"
                  width={120}
                  height={36}
                  priority
                  className="w-full h-auto group-hover:scale-105 transition-transform duration-200"
                />
              </a>

              <a
                href="https://play.google.com/store/apps/details?id=com.TregoTech.TregoApp"
                target="_blank"
                rel="noopener noreferrer"
                className="flex-1 rounded-lg overflow-hidden hover:opacity-80 transition-opacity duration-200 group"
              >
                <Image
                  src="/assets/trego_app/googlePlay.webp"
                  alt="Get it on Google Play"
                  width={120}
                  height={36}
                  priority
                  className="w-full h-auto group-hover:scale-105 transition-transform duration-200"
                />
              </a>
            </div>

            <a
              href="#"
              className="flex items-center justify-center w-full bg-gradient-to-r from-[#302cff] to-[#1e1a99] hover:from-[#1e1a99] hover:to-[#0f0d66] rounded-lg p-2 transition-all duration-200 group"
            >
              <span className="text-white font-semibold font-sora flex items-center space-x-2 text-xs">
                <span className="text-sm">🌐</span>
                <span>Visit Website</span>
              </span>
            </a>
          </div>
        </div>

        {/* Bottom Copyright - Much Shorter */}
        <div className="border-t border-[#302cff]/20 mt-6 pt-4 text-center">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-2 md:space-y-0">
            <p className="text-gray-400 font-sora text-xs">
              © {currentYear} TregoTech. All rights reserved.
            </p>
            <div className="flex space-x-4">
              <Link href="/about" className="text-gray-400 hover:text-[#302cff] transition-colors duration-200 font-sora text-xs">
                About Us
              </Link>
              <Link href="/policies" className="text-gray-400 hover:text-[#302cff] transition-colors duration-200 font-sora text-xs">
                Policies
              </Link>
              <Link href="/terms" className="text-gray-400 hover:text-[#302cff] transition-colors duration-200 font-sora text-xs">
                Terms
              </Link>
              <Link href="/contact" className="text-gray-400 hover:text-[#302cff] transition-colors duration-200 font-sora text-xs">
                Contact
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
