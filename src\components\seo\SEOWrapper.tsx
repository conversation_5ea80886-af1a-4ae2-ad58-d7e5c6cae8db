'use client';

import { usePathname } from 'next/navigation';
import { useEffect } from 'react';
import StructuredData from './StructuredData';
import Breadcrumb from './Breadcrumb';
import { generateBreadcrumbs } from '@/config/seo';

interface SEOWrapperProps {
  children: React.ReactNode;
  pageType?: 'home' | 'about' | 'contact' | 'policies' | 'terms' | 'service';
  customBreadcrumbs?: Array<{ name: string; url: string; current?: boolean }>;
  showBreadcrumbs?: boolean;
  structuredDataType?: 'organization' | 'website' | 'breadcrumb' | 'contactPage' | 'aboutPage';
  customStructuredData?: Record<string, unknown>;
}

export default function SEOWrapper({
  children,
  pageType = 'home',
  customBreadcrumbs,
  showBreadcrumbs = true,
  structuredDataType,
  customStructuredData,
}: SEOWrapperProps) {
  const pathname = usePathname();

  // Generate breadcrumbs based on current path
  const breadcrumbs = customBreadcrumbs || generateBreadcrumbs(pathname);

  // Determine structured data type based on page type
  const getStructuredDataType = () => {
    if (structuredDataType) return structuredDataType;
    
    switch (pageType) {
      case 'about':
        return 'aboutPage';
      case 'contact':
        return 'contactPage';
      default:
        return 'website';
    }
  };

  // Track page views for analytics
  useEffect(() => {
    // Google Analytics page view
    if (typeof window !== 'undefined' && (window as unknown as Record<string, unknown>).gtag) {
      ((window as unknown as Record<string, unknown>).gtag as (...args: unknown[]) => void)('config', process.env.NEXT_PUBLIC_GA_ID, {
        page_path: pathname,
      });
    }

    // Facebook Pixel page view
    if (typeof window !== 'undefined' && (window as unknown as Record<string, unknown>).fbq) {
      ((window as unknown as Record<string, unknown>).fbq as (...args: unknown[]) => void)('track', 'PageView');
    }

    // Custom analytics event
    if (typeof window !== 'undefined') {
      const event = new CustomEvent('pageView', {
        detail: {
          path: pathname,
          pageType,
          timestamp: new Date().toISOString(),
        },
      });
      window.dispatchEvent(event);
    }
  }, [pathname, pageType]);

  // Add JSON-LD structured data to head
  useEffect(() => {
    const addStructuredData = () => {
      // Remove existing structured data
      const existingScripts = document.querySelectorAll('script[type="application/ld+json"]');
      existingScripts.forEach(script => {
        if (script.textContent?.includes('"@context": "https://schema.org"')) {
          script.remove();
        }
      });

      // Add page-specific structured data
      const structuredDataScript = document.createElement('script');
      structuredDataScript.type = 'application/ld+json';
      
      let structuredData;
      if (customStructuredData) {
        structuredData = customStructuredData;
      } else {
        // Generate default structured data based on page type
        structuredData = generateDefaultStructuredData(pageType, pathname);
      }

      structuredDataScript.textContent = JSON.stringify(structuredData, null, 2);
      document.head.appendChild(structuredDataScript);
    };

    addStructuredData();

    return () => {
      // Cleanup on unmount
      const scripts = document.querySelectorAll('script[type="application/ld+json"]');
      scripts.forEach(script => {
        if (script.textContent?.includes(pathname)) {
          script.remove();
        }
      });
    };
  }, [pathname, pageType, customStructuredData]);

  return (
    <>
      {/* Structured Data Components */}
      <StructuredData type={getStructuredDataType()} data={customStructuredData} />
      
      {/* Breadcrumb Navigation */}
      {showBreadcrumbs && breadcrumbs.length > 1 && (
        <div className="container mx-auto px-4 py-4">
          <Breadcrumb items={breadcrumbs} />
        </div>
      )}

      {/* Page Content */}
      {children}
    </>
  );
}

// Helper function to generate default structured data
function generateDefaultStructuredData(pageType: string, pathname: string) {
  const baseUrl = 'https://tregotech.com';
  
  const baseData = {
    '@context': 'https://schema.org',
    '@type': 'WebPage',
    name: getPageTitle(pageType),
    description: getPageDescription(pageType),
    url: `${baseUrl}${pathname}`,
    isPartOf: {
      '@type': 'WebSite',
      name: 'TregoTech',
      url: baseUrl,
    },
    about: {
      '@type': 'Organization',
      name: 'TregoTech',
      description: 'Leading technology solutions provider in Egypt',
    },
  };

  // Add page-specific data
  switch (pageType) {
    case 'about':
      return {
        ...baseData,
        '@type': 'AboutPage',
        mainEntity: {
          '@type': 'Organization',
          name: 'TregoTech',
          description: 'Pioneering the future of travel technology in Egypt and beyond',
          foundingDate: '2023',
        },
      };

    case 'contact':
      return {
        ...baseData,
        '@type': 'ContactPage',
        mainEntity: {
          '@type': 'Organization',
          name: 'TregoTech',
          contactPoint: {
            '@type': 'ContactPoint',
            telephone: '+201112628619',
            email: '<EMAIL>',
            contactType: 'customer service',
          },
        },
      };

    case 'service':
      return {
        ...baseData,
        '@type': 'Service',
        provider: {
          '@type': 'Organization',
          name: 'TregoTech',
        },
        serviceType: 'Technology Solutions',
        areaServed: 'Egypt',
      };

    default:
      return baseData;
  }
}

// Helper functions
function getPageTitle(pageType: string): string {
  const titles = {
    home: 'TregoTech - Leading Technology Solutions',
    about: 'About TregoTech - Pioneering Travel Technology',
    contact: 'Contact TregoTech - Expert Technology Solutions',
    policies: 'TregoTech Privacy Policy - Data Protection',
    terms: 'TregoTech Terms of Service - Service Terms',
    service: 'TregoTech Services - Technology Solutions',
  };
  
  return titles[pageType as keyof typeof titles] || titles.home;
}

function getPageDescription(pageType: string): string {
  const descriptions = {
    home: 'TregoTech provides innovative mobile app development, web development, and AI automation solutions.',
    about: 'Learn about TregoTech\'s mission to revolutionize travel technology in Egypt.',
    contact: 'Contact TregoTech for innovative technology solutions and expert consultation.',
    policies: 'Read TregoTech\'s comprehensive privacy policy and data protection practices.',
    terms: 'Understand TregoTech\'s terms of service and conditions for our technology solutions.',
    service: 'Explore TregoTech\'s comprehensive technology services and solutions.',
  };
  
  return descriptions[pageType as keyof typeof descriptions] || descriptions.home;
}
