import { ContactFormData, ContactFormErrors } from "../hooks/useContact";

interface ContactFormProps {
  formRef: React.RefObject<HTMLDivElement | null>;
  formData: ContactFormData;
  errors: ContactFormErrors;
  isSubmitting: boolean;
  submitStatus: 'idle' | 'success' | 'error';
  handleInputChange: (field: keyof ContactFormData, value: string) => void;
  handleSubmit: (e: React.FormEvent) => void;
}

export default function ContactForm({ 
  formRef, 
  formData, 
  errors, 
  isSubmitting, 
  submitStatus, 
  handleInputChange, 
  handleSubmit 
}: ContactFormProps) {
  return (
    <div ref={formRef} className="lg:col-span-2">
      <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-xl rounded-3xl p-8 md:p-12 border border-gray-700/30 shadow-2xl">
        <h2 className="text-3xl font-bold text-white mb-8 font-orbitron text-center">
          Send us a Message
        </h2>

        {submitStatus === 'success' && (
          <div className="mb-8 p-4 bg-green-500/10 border border-green-500/30 rounded-xl">
            <p className="text-green-400 font-sora text-center">
              Thank you for your message! We&apos;ll get back to you soon.
            </p>
          </div>
        )}

        {submitStatus === 'error' && (
          <div className="mb-8 p-4 bg-red-500/10 border border-red-500/30 rounded-xl">
            <p className="text-red-400 font-sora text-center">
              Something went wrong. Please try again later.
            </p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Name Field */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2 font-sora">
                Full Name *
              </label>
              <input
                type="text"
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={`w-full px-4 py-3 bg-gray-700/50 border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#302cff] focus:border-transparent transition-all duration-300 font-sora ${
                  errors.name ? 'border-red-500' : 'border-gray-600/50'
                }`}
                placeholder="Enter your full name"
                disabled={isSubmitting}
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-400 font-sora">{errors.name}</p>
              )}
            </div>

            {/* Email Field */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2 font-sora">
                Email Address *
              </label>
              <input
                type="email"
                id="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={`w-full px-4 py-3 bg-gray-700/50 border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#302cff] focus:border-transparent transition-all duration-300 font-sora ${
                  errors.email ? 'border-red-500' : 'border-gray-600/50'
                }`}
                placeholder="Enter your email address"
                disabled={isSubmitting}
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-400 font-sora">{errors.email}</p>
              )}
            </div>
          </div>

          {/* Phone Field */}
          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-300 mb-2 font-sora">
              Phone Number
            </label>
            <input
              type="tel"
              id="phone"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#302cff] focus:border-transparent transition-all duration-300 font-sora"
              placeholder="Enter your phone number (optional)"
              disabled={isSubmitting}
            />
          </div>

          {/* Subject Field */}
          <div>
            <label htmlFor="subject" className="block text-sm font-medium text-gray-300 mb-2 font-sora">
              Subject *
            </label>
            <input
              type="text"
              id="subject"
              value={formData.subject}
              onChange={(e) => handleInputChange('subject', e.target.value)}
              className={`w-full px-4 py-3 bg-gray-700/50 border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#302cff] focus:border-transparent transition-all duration-300 font-sora ${
                errors.subject ? 'border-red-500' : 'border-gray-600/50'
              }`}
              placeholder="What's this about?"
              disabled={isSubmitting}
            />
            {errors.subject && (
              <p className="mt-1 text-sm text-red-400 font-sora">{errors.subject}</p>
            )}
          </div>

          {/* Message Field */}
          <div>
            <label htmlFor="message" className="block text-sm font-medium text-gray-300 mb-2 font-sora">
              Message *
            </label>
            <textarea
              id="message"
              rows={6}
              value={formData.message}
              onChange={(e) => handleInputChange('message', e.target.value)}
              className={`w-full px-4 py-3 bg-gray-700/50 border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#302cff] focus:border-transparent transition-all duration-300 font-sora resize-none ${
                errors.message ? 'border-red-500' : 'border-gray-600/50'
              }`}
              placeholder="Tell us about your project or inquiry..."
              disabled={isSubmitting}
            ></textarea>
            {errors.message && (
              <p className="mt-1 text-sm text-red-400 font-sora">{errors.message}</p>
            )}
          </div>

          {/* Submit Button */}
          <div className="text-center pt-4">
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-8 py-4 bg-gradient-to-r from-[#302cff] to-purple-600 text-white font-bold font-sora rounded-xl shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-[#302cff]/30 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
            >
              {isSubmitting ? 'Sending...' : 'Send Message'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
