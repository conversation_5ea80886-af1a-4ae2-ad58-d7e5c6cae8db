"use client";

import { useEffect, useState } from "react";

export default function SmoothBackgroundTransition() {

  const [landingToServicesProgress, setLandingToServicesProgress] = useState(0);
  const [servicesToMobileProgress, setServicesToMobileProgress] = useState(0);
  const [isMounted, setIsMounted] = useState(false);

  // Prevent hydration mismatch
  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (!isMounted) return;

    const handleScroll = () => {
      const landingSection = document.getElementById('landing');
      const servicesSection = document.getElementById('services');
      const mobileSection = document.getElementById('mobile-app-showcase');

      if (!landingSection || !servicesSection || !mobileSection) return;

      const landingRect = landingSection.getBoundingClientRect();
      const servicesRect = servicesSection.getBoundingClientRect();

      const windowHeight = window.innerHeight;

      // Landing to Services transition (Black/Dark Blue to Grey/Dark Blue)
      let landingProgress = 0;
      if (landingRect.bottom <= windowHeight * 1.2 && landingRect.bottom >= -windowHeight * 0.2) {
        const transitionZone = windowHeight * 0.6; // Longer transition zone
        const scrollPastLanding = (windowHeight * 1.2) - landingRect.bottom;
        landingProgress = Math.min(1, Math.max(0, scrollPastLanding / transitionZone));
      } else if (landingRect.bottom < -windowHeight * 0.2) {
        landingProgress = 1;
      }

      // Services to Mobile transition (Grey/Dark Blue to White/Dark Blue)
      let servicesProgress = 0;
      if (servicesRect.bottom <= windowHeight * 1.2 && servicesRect.bottom >= -windowHeight * 0.2) {
        const transitionZone = windowHeight * 0.6; // Longer transition zone
        const scrollPastServices = (windowHeight * 1.2) - servicesRect.bottom;
        servicesProgress = Math.min(1, Math.max(0, scrollPastServices / transitionZone));
      } else if (servicesRect.bottom < -windowHeight * 0.2) {
        servicesProgress = 1;
      }

      setLandingToServicesProgress(landingProgress);
      setServicesToMobileProgress(servicesProgress);
    };

    // Initial check
    handleScroll();

    // Add scroll listener with throttling for performance
    let ticking = false;
    const throttledScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', throttledScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', throttledScroll);
    };
  }, [isMounted]);

  return (
    <>
      {/* Base Black Background - Always Present to Prevent White Flashes */}
      <div className="fixed inset-0 z-[-10] bg-black" />

      {/* Landing Section - Black/Dark Blue Background */}
      <div
        className="fixed inset-0 z-[1] pointer-events-none transition-all duration-[1500ms] ease-in-out"
        style={{
          opacity: 1 - landingToServicesProgress,
          background: `
            radial-gradient(circle at 30% 70%, rgba(48, 44, 255, 0.4) 0%, transparent 60%),
            radial-gradient(circle at 70% 30%, rgba(30, 26, 153, 0.3) 0%, transparent 60%),
            linear-gradient(135deg, #000000 0%, #0a0a1a 50%, #1b1b2d 100%)
          `
        }}
      />

      {/* Services Section - Grey/Dark Blue Background */}
      <div
        className="fixed inset-0 z-[1] pointer-events-none transition-all duration-[1500ms] ease-in-out"
        style={{
          opacity: landingToServicesProgress * (1 - servicesToMobileProgress),
          background: `
            radial-gradient(circle at 40% 60%, rgba(48, 44, 255, 0.2) 0%, transparent 70%),
            radial-gradient(circle at 60% 40%, rgba(30, 26, 153, 0.15) 0%, transparent 70%),
            linear-gradient(135deg, #2a2a2a 0%, #2a2a3a 50%, #3a3a4a 100%)
          `
        }}
      />

      {/* Mobile App Section - Darker Dark Blue Background */}
      <div
        className="fixed inset-0 z-[1] pointer-events-none transition-all duration-[1500ms] ease-in-out"
        style={{
          opacity: servicesToMobileProgress,
          background: `
            radial-gradient(circle at 40% 60%, rgba(48, 44, 255, 0.35) 0%, transparent 50%),
            radial-gradient(circle at 60% 40%, rgba(30, 26, 153, 0.30) 0%, transparent 50%),
            radial-gradient(circle at 20% 80%, rgba(48, 44, 255, 0.25) 0%, transparent 60%),
            radial-gradient(circle at 80% 20%, rgba(30, 26, 153, 0.20) 0%, transparent 60%),
            linear-gradient(135deg, #1a1a1a 0%, #1a1a2a 30%, #2a2a3a 70%, #1b1b2d 100%)
          `
        }}
      />

      {/* Remove problematic transition overlays that cause white flashes */}

      {/* Dynamic Dark Blue Particles - Only render on client to prevent hydration mismatch */}
      {isMounted && (
        <div className="fixed inset-0 z-[2] pointer-events-none overflow-hidden">
          {[...Array(8)].map((_, i) => (
            <div
              key={i}
              className="absolute rounded-full transition-all duration-1000 ease-out"
              style={{
                left: `${15 + i * 12}%`,
                top: `${25 + (i % 3) * 25}%`,
                width: `${2 + (i % 2)}px`,
                height: `${2 + (i % 2)}px`,
                backgroundColor: servicesToMobileProgress > 0.5
                  ? `rgba(48, 44, 255, ${0.4 + (i % 3) * 0.1})`
                  : landingToServicesProgress > 0.5
                  ? `rgba(30, 26, 153, ${0.3 + (i % 3) * 0.1})`
                  : `rgba(30, 26, 153, ${0.5 + (i % 3) * 0.1})`,
                transform: `translateY(${(i % 2 === 0 ? 1 : -1) * (5 + i % 3 * 3)}px) scale(${0.8 + (i % 3) * 0.2})`,
                animation: `float-${(i % 3) + 1} ${5 + i * 0.5}s ease-in-out infinite`
              }}
            />
          ))}
        </div>
      )}
    </>
  );
}
