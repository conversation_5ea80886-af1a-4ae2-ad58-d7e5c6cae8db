{"name": "tregotech", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo -p 4020", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@lottiefiles/dotlottie-react": "^0.14.2", "@splinetool/react-spline": "^4.0.0", "axios": "^1.4.0", "framer-motion": "^12.19.1", "gsap": "^3.13.0", "leaflet": "^1.9.4", "lenis": "^1.3.4", "lottie-react": "^2.4.1", "lottie-web": "^5.13.0", "lucide-react": "^0.525.0", "next": "15.3.4", "next-auth": "^4.24.11", "next-intl": "^4.3.1", "next-seo": "^6.8.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-lottie": "^1.2.10", "react-toastify": "^11.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/axios": "^0.14.4", "@types/gsap": "^3.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-lottie": "^1.2.10", "autoprefixer": "^10.4.21", "depcheck": "^1.4.7", "eslint": "^9", "eslint-config-next": "15.3.4", "postcss": "^8.5.6", "tailwindcss": "^4.1.10", "typescript": "^5"}}