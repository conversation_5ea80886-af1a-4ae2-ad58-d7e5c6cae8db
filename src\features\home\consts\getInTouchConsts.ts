export const CONTACT_INFO = {
  email: "<EMAIL>",
  phone: "+201112628619"
};

export const SECTION_CONTENT = {
  title: "Get In Touch",
  description: "Ready to transform your business with AI? Let's discuss your project."
};

export const CONTACT_METHODS = [
  {
    icon: "✉️",
    title: "Email Us",
    value: CONTACT_INFO.email,
    type: "email"
  },
  {
    icon: "📱",
    title: "Call Us",
    value: CONTACT_INFO.phone,
    type: "phone"
  }
];

export const FAQ_DATA = [
  {
    question: "What services does Trego Tech offer?",
    answer: "Trego Tech is a software development company specializing in creating innovative solutions. Our flagship product, Trego, is a mobile application that helps users book trains, buses, and flights."
  },
  {
    question: "How can I partner with Trego Tech?",
    answer: "We'd love to collaborate. Simply reach out to us through our contact form on the website. We offer API integrations, software development, and other partnership opportunities to help grow your business."
  },
  {
    question: "Will Trego Tech release more products besides the Trego app?",
    answer: "Yes, Trego Tech plans to diversify its product offerings in the future. While the Trego app is our current focus, we are continuously exploring new ideas and solutions that can benefit various industries."
  },
  {
    question: "What is the vision of Trego Tech?",
    answer: "At Trego Tech, our vision is to create innovative software solutions that connect people and enhance their experiences across various industries."
  },
  {
    question: "What industries is Trego Tech planning to enter in the future?",
    answer: "While Trego Tech is initially focused on the travel industry, we are committed to exploring expansion into other industries."
  },
  {
    question: "How does Trego Tech ensure quality in its software products?",
    answer: "We prioritize quality at every stage of development at Trego Tech. Our team follows best practices in software engineering, including rigorous testing, code reviews, and continuous integration."
  }
];
