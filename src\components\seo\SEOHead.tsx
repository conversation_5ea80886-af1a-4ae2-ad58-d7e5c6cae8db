'use client';

import Head from 'next/head';

interface SEOHeadProps {
  canonical?: string;
  hreflang?: {
    [key: string]: string;
  };
  noindex?: boolean;
  nofollow?: boolean;
}

export default function SEOHead({
  canonical,
  hreflang,
  noindex = false,
  nofollow = false,
}: SEOHeadProps) {
  const baseUrl = 'https://tregotech.com';
  
  // Default hreflang if not provided
  const defaultHreflang = {
    'en': baseUrl,
    'ar': `${baseUrl}/ar`,
    'x-default': baseUrl,
  };

  const finalHreflang = hreflang || defaultHreflang;
  
  // Robots meta content
  const robotsContent = [];
  if (noindex) robotsContent.push('noindex');
  else robotsContent.push('index');
  
  if (nofollow) robotsContent.push('nofollow');
  else robotsContent.push('follow');

  return (
    <Head>
      {/* Canonical URL */}
      {canonical && <link rel="canonical" href={canonical} />}
      
      {/* Hreflang tags */}
      {Object.entries(finalHreflang).map(([lang, url]) => (
        <link
          key={lang}
          rel="alternate"
          hrefLang={lang}
          href={url}
        />
      ))}
      
      {/* Robots meta */}
      <meta name="robots" content={robotsContent.join(', ')} />
      
      {/* Additional SEO meta tags */}
      <meta name="googlebot" content="index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1" />
      <meta name="bingbot" content="index, follow" />
      
      {/* Preconnect to external domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://prod.spline.design" />
      
      {/* DNS prefetch for performance */}
      <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
      <link rel="dns-prefetch" href="https://fonts.gstatic.com" />
      <link rel="dns-prefetch" href="https://prod.spline.design" />
      
      {/* Viewport and mobile optimization */}
      <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
      <meta name="format-detection" content="telephone=no" />
      
      {/* Theme color for mobile browsers */}
      <meta name="theme-color" content="#302cff" />
      <meta name="msapplication-TileColor" content="#302cff" />
      
      {/* Apple specific meta tags */}
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
      <meta name="apple-mobile-web-app-title" content="TregoTech" />
      
      {/* Microsoft specific meta tags */}
      <meta name="msapplication-config" content="/browserconfig.xml" />
      
      {/* Additional performance hints */}
      <link rel="preload" href="/fonts/Orbitron-Regular.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />
      <link rel="preload" href="/fonts/Sora-Regular.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />
    </Head>
  );
}
