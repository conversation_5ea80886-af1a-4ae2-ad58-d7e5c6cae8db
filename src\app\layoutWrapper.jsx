"use client";

import { memo } from "react";
import FuturisticCursor from "@/components/shared/components/FuturisticCursor";
import SmoothBackgroundTransition from "@/components/shared/components/SmoothBackgroundTransition";
import PerformanceTracker from "@/components/shared/components/PerformanceTracker";
import PerformanceOptimizer from "@/components/shared/components/PerformanceOptimizer";
// import Header from "../../components/shared/Header";

const LayoutWrapper = memo(function LayoutWrapper({ children }) {

  return (
    <>
      {/* CRITICAL: Performance optimizations for LCP */}
      <PerformanceOptimizer />

      {/* Smooth Background Transition from Grey to White */}
      <SmoothBackgroundTransition />

      {/* Futuristic Cursor */}
      <FuturisticCursor />

      {/* Performance Tracker - Only in development */}
      {process.env.NODE_ENV === 'development' && <PerformanceTracker />}

      {/* {!hide && <Header />} */}
      <main className="relative z-10">{children}</main>

    </>
  );
});

export default LayoutWrapper;
