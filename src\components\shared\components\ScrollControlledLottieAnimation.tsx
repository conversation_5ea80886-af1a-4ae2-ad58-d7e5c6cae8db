"use client";

import lottie from 'lottie-web';
import { useEffect, useRef } from 'react';

interface ScrollControlledLottieAnimationProps {
  animationData: unknown;
  className?: string;
  loop?: boolean;
  autoplay?: boolean;
  renderer?: 'svg' | 'canvas' | 'html';
  style?: React.CSSProperties;
  onInstanceReady?: (instance: { play: () => void; pause: () => void }) => void; // Callback when lottie instance is ready
}

export default function ScrollControlledLottieAnimation({ 
  animationData, 
  className = "",
  loop = true,
  autoplay = true,
  renderer = 'svg',
  style = {},
  onInstanceReady
}: ScrollControlledLottieAnimationProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<{ destroy: () => void } | null>(null);

  useEffect(() => {
    if (!containerRef.current || !animationData) return;

    // Destroy previous animation if it exists
    if (animationRef.current) {
      animationRef.current.destroy();
    }

    // Create new animation
    animationRef.current = lottie.loadAnimation({
      container: containerRef.current,
      renderer: renderer,
      loop: loop,
      autoplay: autoplay,
      animationData: animationData,
      rendererSettings: {
        preserveAspectRatio: 'xMidYMid slice',
        progressiveLoad: true,
        // Optimize for performance
        clearCanvas: true,
        hideOnTransparent: true,
      },
    });

    // Notify parent component that instance is ready
    if (onInstanceReady && animationRef.current) {
      onInstanceReady(animationRef.current as unknown as { play: () => void; pause: () => void });
    }

    // Cleanup function
    return () => {
      if (animationRef.current) {
        animationRef.current.destroy();
        animationRef.current = null;
      }
    };
  }, [animationData, loop, autoplay, renderer, onInstanceReady]);

  return (
    <div
      ref={containerRef}
      className={className}
      style={{
        willChange: 'transform',
        backfaceVisibility: 'hidden',
        transform: 'translateZ(0)',
        ...style
      }}
    />
  );
}
