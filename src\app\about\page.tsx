import AboutUsMain from "@/features/about/components/AboutUsMain";
import StructuredData from "@/components/seo/StructuredData";
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'About Us - TregoTech | Leading Technology Solutions in Egypt',
  description: 'Learn about TregoTech\'s mission to revolutionize travel technology in Egypt. Discover our vision, values, and commitment to innovative mobile app development, web solutions, and AI automation.',
  keywords: 'about TregoTech, travel technology Egypt, mobile app development company, AI automation, web development, technology solutions, Egyptian tech company, innovation, mission, vision',
  authors: [{ name: 'TregoTech' }],
  creator: 'TregoTech',
  publisher: 'TregoTech',
  openGraph: {
    title: 'About TregoTech - Pioneering Travel Technology in Egypt',
    description: 'Discover TregoTech\'s journey in revolutionizing travel technology. Learn about our mission, vision, and values that drive innovation in mobile app development and AI solutions.',
    url: 'https://tregotech.com/about',
    siteName: 'TregoTech',
    images: [
      {
        url: '/assets/BlueLogo.png',
        width: 1200,
        height: 630,
        alt: 'TregoTech - About Us',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'About TregoTech - Pioneering Travel Technology in Egypt',
    description: 'Discover TregoTech\'s journey in revolutionizing travel technology. Learn about our mission, vision, and values.',
    images: ['/assets/BlueLogo.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: 'https://tregotech.com/about',
    languages: {
      'en': 'https://tregotech.com/about',
      'ar': 'https://tregotech.com/ar/about',
    },
  },
};

export default function AboutPage() {
  return (
    <>
      <StructuredData type="aboutPage" />
      <StructuredData
        type="breadcrumb"
        data={{
          breadcrumbs: [
            { name: "Home", url: "/" },
            { name: "About Us", url: "/about" }
          ]
        }}
      />
      <AboutUsMain />
    </>
  )
}