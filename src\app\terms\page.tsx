import TermsMain from "@/features/terms/components/TermsMain";
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Terms of Service - TregoTech | Service Terms & Conditions',
  description: 'Read TregoTech\'s terms of service and conditions. Understand the terms governing the use of our mobile app development, web development, and AI automation services.',
  keywords: 'TregoTech terms of service, terms and conditions, service agreement, mobile app terms, web development terms, AI automation terms, user agreement, legal terms',
  authors: [{ name: 'TregoTech' }],
  creator: 'TregoTech',
  publisher: 'TregoTech',
  openGraph: {
    title: 'TregoTech Terms of Service - Service Terms & Conditions',
    description: 'Understand the terms and conditions governing TregoTech\'s technology services. Comprehensive terms of service for our solutions.',
    url: 'https://tregotech.com/terms',
    siteName: 'TregoTech',
    images: [
      {
        url: '/assets/BlueLogo.png',
        width: 1200,
        height: 630,
        alt: 'TregoTech Terms of Service',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'TregoTech Terms of Service - Service Terms & Conditions',
    description: 'Understand the terms and conditions governing TregoTech\'s technology services.',
    images: ['/assets/BlueLogo.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: 'https://tregotech.com/terms',
    languages: {
      'en': 'https://tregotech.com/terms',
      'ar': 'https://tregotech.com/ar/terms',
    },
  },
};

export default function TermsPage() {
  return (
    <>
      <TermsMain />
    </>
  );
}
