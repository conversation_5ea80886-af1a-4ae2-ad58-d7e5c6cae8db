"use client";

import ScrollControlledLottieAnimation from "@/components/shared/components/ScrollControlledLottieAnimation";
import { useLanding } from "../hooks/useLanding";
import animation from '../../../../public/assets/landing/NewAnimationLottie.json';

export default function Landing() {
  const {
    refs: { titleRef, subtitleRef, descriptionRef, animationRef },
    registerLottieInstance
  } = useLanding();

  return (
    <section id="landing" className="min-h-screen relative overflow-hidden">



      <div className="relative z-10 min-h-screen flex items-center justify-center px-8 lg:px-16 pt-20 md:pt-0">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center min-h-screen">

            <div className="flex items-center justify-start">
              <div className="space-y-8">
                <div ref={titleRef} className="landing-element space-y-6">
                  <div>
                    <h1 className="text-4xl md:text-6xl lg:text-7xl font-black leading-none text-left">
                      <span className="block text-white font-orbitron tracking-tight drop-shadow-xl">TREGO</span>
                      <span className="block bg-gradient-to-r from-[#4d42ff] via-[#302cff] to-[#1e1a99] bg-clip-text text-transparent font-orbitron tracking-tight drop-shadow-lg">
                        TECH
                      </span>
                    </h1>
                  </div>
                </div>

                <div ref={subtitleRef} className="landing-element space-y-3">
                  <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-white/95 font-sora leading-tight text-left">
                    Building the <span className="bg-gradient-to-r from-[#4d42ff] to-[#302cff] bg-clip-text text-transparent">Future</span> of Digital Innovation
                  </h2>
                </div>

                <div ref={descriptionRef} className="landing-element space-y-6">
                  <p className="text-lg md:text-xl text-white/90 font-sora leading-relaxed text-left">
                    We create <span className="text-[#4d42ff] font-semibold">revolutionary mobile apps</span>,
                    intelligent web platforms, and <span className="text-[#4d42ff] font-semibold">AI-powered solutions</span>
                  </p>
                  <p className="text-base md:text-lg text-white/70 font-sora leading-relaxed text-left">
                    Transform your business and shape tomorrow&apos;s digital landscape with cutting-edge technology
                  </p>
                </div>
              </div>
            </div>

            <div ref={animationRef} className="landing-element flex items-center justify-center lg:justify-end">
              <div className="w-full max-w-lg lg:max-w-2xl">
                <div className="aspect-square flex items-center justify-center relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-[#0a0a0a]/80 via-[#1a1a2e]/80 to-[#16213e]/80 rounded-3xl transition-opacity duration-1000" />

                  <div className="relative w-full h-full rounded-3xl overflow-hidden">
                    <ScrollControlledLottieAnimation
                      animationData={animation}
                      loop={true}
                      autoplay={true}
                      className="w-full h-full"
                      style={{
                        backgroundColor: 'transparent',
                      }}
                      onInstanceReady={registerLottieInstance}
                    />
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </section>
  );
}
