"use client";

import { useRef, useEffect, useState } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { initialFormData, FORM_SUBMIT_DELAY } from "../consts/becomePartnerConsts";

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

export const useBecomePartner = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLDivElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const advantagesRef = useRef<HTMLDivElement>(null);
  const formRef = useRef<HTMLDivElement>(null);

  const [formData, setFormData] = useState(initialFormData);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const ctx = gsap.context(() => {
      // Set initial states for all elements
      gsap.set(".partner-title", { opacity: 0, y: -50, scale: 0.9 });
      gsap.set(".partner-subtitle", { opacity: 0, y: -30, scale: 0.95 });
      gsap.set(".partner-line", { opacity: 0, scaleX: 0 });
      gsap.set(".partner-benefit-item", { opacity: 0, y: -50, scale: 0.9 });
      gsap.set(".partner-image", { opacity: 0, y: -40, scale: 0.8 });

      // Entrance animations - from top
      ScrollTrigger.create({
        trigger: ".partner-title",
        start: "top 85%",
        end: "bottom 55%",
        scrub: 0.5,
        animation: gsap.to(".partner-title", {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 1.2,
          ease: "power2.out"
        })
      });

      ScrollTrigger.create({
        trigger: ".partner-subtitle",
        start: "top 80%",
        end: "bottom 50%",
        scrub: 0.5,
        animation: gsap.to(".partner-subtitle", {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 1,
          ease: "power2.out"
        })
      });

      // Animate the line under the title
      ScrollTrigger.create({
        trigger: ".partner-line",
        start: "top 80%",
        end: "bottom 50%",
        scrub: 0.4,
        animation: gsap.to(".partner-line", {
          opacity: 1,
          scaleX: 1,
          duration: 1,
          ease: "power2.out"
        })
      });

      ScrollTrigger.create({
        trigger: ".partner-image",
        start: "top 75%",
        end: "bottom 45%",
        scrub: 0.6,
        animation: gsap.to(".partner-image", {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 1.2,
          ease: "power2.out"
        })
      });

      ScrollTrigger.create({
        trigger: ".partner-benefit-item",
        start: "top 80%",
        end: "bottom 50%",
        scrub: 0.6,
        animation: gsap.to(".partner-benefit-item", {
          opacity: 1,
          y: 0,
          scale: 1,
          stagger: 0.1,
          duration: 1,
          ease: "power2.out"
        })
      });

      // Exiting animations - only when scrolling down/away to bottom
      ScrollTrigger.create({
        trigger: sectionRef.current,
        start: "bottom 60%",
        end: "bottom 10%",
        scrub: 0.4,
        animation: gsap.to(".partner-title", {
          opacity: 0,
          y: 30,
          scale: 0.95,
          duration: 0.8,
          ease: "power2.in"
        })
      });

      ScrollTrigger.create({
        trigger: sectionRef.current,
        start: "bottom 55%",
        end: "bottom 5%",
        scrub: 0.4,
        animation: gsap.to(".partner-subtitle", {
          opacity: 0,
          y: 20,
          scale: 0.95,
          duration: 0.8,
          ease: "power2.in"
        })
      });

      ScrollTrigger.create({
        trigger: sectionRef.current,
        start: "bottom 50%",
        end: "bottom 0%",
        scrub: 0.4,
        animation: gsap.to(".partner-line", {
          opacity: 0,
          scaleX: 0,
          duration: 0.8,
          ease: "power2.in"
        })
      });

      ScrollTrigger.create({
        trigger: sectionRef.current,
        start: "bottom 50%",
        end: "bottom 0%",
        scrub: 0.5,
        animation: gsap.to(".partner-benefit-item", {
          opacity: 0,
          y: 30,
          scale: 0.9,
          stagger: 0.05,
          duration: 0.8,
          ease: "power2.in"
        })
      });

      ScrollTrigger.create({
        trigger: sectionRef.current,
        start: "bottom 45%",
        end: "bottom -5%",
        scrub: 0.5,
        animation: gsap.to(".partner-image", {
          opacity: 0,
          y: 40,
          scale: 0.8,
          duration: 0.8,
          ease: "power2.in"
        })
      });
    }, sectionRef);

    return () => ctx.revert();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      await new Promise(resolve => setTimeout(resolve, FORM_SUBMIT_DELAY));
      console.log('Partner application submitted:', formData);
      setSubmitStatus('success');
      setFormData(initialFormData);
    } catch (error) {
      console.error('Error submitting form:', error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    refs: {
      sectionRef,
      titleRef,
      subtitleRef,
      advantagesRef,
      formRef
    },
    formData,
    isSubmitting,
    submitStatus,
    handleInputChange,
    handleSubmit
  };
};
