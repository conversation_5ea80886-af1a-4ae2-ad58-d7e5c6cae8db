"use client";

import { useState } from "react";
import { TermsData } from "@/types/policy";
import { useTerms as useOriginalTerms } from "@/hooks/useTerms";

export const useTerms = () => {
  const { terms, loading, error, refetch } = useOriginalTerms();
  const [language, setLanguage] = useState<'en' | 'ar'>('en');

  const getTitle = (term: TermsData) => {
    return language === 'en' ? term.title_en : term.title_ar;
  };

  const getSubtitle = (term: TermsData) => {
    return language === 'en' ? term.subtitle_en : term.subtitle_ar;
  };

  const getDescription = (term: TermsData) => {
    return language === 'en' ? term.description_en : term.description_ar;
  };

  return {
    terms,
    loading,
    error,
    refetch,
    language,
    setLanguage,
    getTitle,
    getSubtitle,
    getDescription
  };
};
