import { useRouter, usePathname } from 'next/navigation';
import { useLocale } from 'next-intl';

const locale = useLocale();
const router = useRouter();
const pathname = usePathname();

export const switchLocale = (newLocale: string) => {
  const segments = pathname.split('/');
  segments[1] = newLocale; // assuming your locale is the first part of the URL
  const newPath = segments.join('/');
  router.push(newPath);
};
