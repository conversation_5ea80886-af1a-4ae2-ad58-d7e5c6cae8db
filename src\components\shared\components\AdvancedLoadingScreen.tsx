"use client";

import { useAdvancedLoading } from "@/hooks/useAdvancedLoading";
import Spinner from "./Spinner";

export default function AdvancedLoadingScreen() {
  const { isLoading, progress } = useAdvancedLoading();

  console.log('AdvancedLoadingScreen render - isLoading:', isLoading, 'progress:', progress.percentage);

  if (!isLoading) return null;

  return (
    <div
      data-loading-screen
      className="fixed inset-0 z-50 bg-gradient-to-br from-[#0a0a0a] via-[#1a1a2e] to-[#16213e] flex items-center justify-center"
    >
      <div className="flex flex-col items-center justify-center text-center max-w-4xl mx-auto px-4">
        {/* PERFECTLY CENTERED SPINNER */}
        <div className="flex items-center justify-center mb-8">
          <Spinner />
        </div>

        {/* CENTERED CONTENT - Enhanced Loading Text and Progress Bar */}
        <div className="space-y-8 flex flex-col items-center">
          {/* Enhanced Animated Loading Text */}
          <div className="text-center animate-fade-in-up">
            <h2 className="text-3xl md:text-4xl font-bold text-white font-orbitron animate-text-glow">
              <span className="animate-letter-wave">L</span>
              <span className="animate-letter-wave" style={{ animationDelay: '0.1s' }}>o</span>
              <span className="animate-letter-wave" style={{ animationDelay: '0.2s' }}>a</span>
              <span className="animate-letter-wave" style={{ animationDelay: '0.3s' }}>d</span>
              <span className="animate-letter-wave" style={{ animationDelay: '0.4s' }}>i</span>
              <span className="animate-letter-wave" style={{ animationDelay: '0.5s' }}>n</span>
              <span className="animate-letter-wave" style={{ animationDelay: '0.6s' }}>g</span>
              <span className="inline-block animate-dot-bounce ml-2">.</span>
              <span className="inline-block animate-dot-bounce ml-1" style={{ animationDelay: '0.3s' }}>.</span>
              <span className="inline-block animate-dot-bounce ml-1" style={{ animationDelay: '0.6s' }}>.</span>
            </h2>
          </div>

          {/* Enhanced Progress Bar with Animations */}
          <div className="w-80 relative">
            {/* Background Track */}
            <div className="bg-gray-800/50 rounded-full h-4 shadow-inner overflow-hidden">
              {/* Animated Background Shimmer */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shimmer"></div>

              {/* Main Progress Bar */}
              <div
                className="bg-gradient-to-r from-[#BA42FF] via-[#8B5CF6] to-[#00E1FF] h-4 rounded-full transition-all duration-300 ease-out relative overflow-hidden animate-progress-glow"
                style={{
                  width: `${progress.percentage}%`,
                  boxShadow: '0 0 20px rgba(186, 66, 255, 0.6), 0 0 40px rgba(0, 225, 255, 0.4)'
                }}
              >
                {/* Moving Light Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-progress-light"></div>

                {/* Pulsing Glow */}
                <div className="absolute inset-0 bg-gradient-to-r from-[#BA42FF]/50 to-[#00E1FF]/50 animate-pulse"></div>
              </div>
            </div>

            {/* Progress Particles */}
            <div className="absolute -top-2 left-0 w-full h-8 pointer-events-none">
              <div className="animate-particle-1 absolute w-1 h-1 bg-[#BA42FF] rounded-full shadow-[0_0_6px_rgba(186,66,255,0.8)]"></div>
              <div className="animate-particle-2 absolute w-1 h-1 bg-[#00E1FF] rounded-full shadow-[0_0_6px_rgba(0,225,255,0.8)]"></div>
              <div className="animate-particle-3 absolute w-1 h-1 bg-[#8B5CF6] rounded-full shadow-[0_0_6px_rgba(139,92,246,0.8)]"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
