"use client";

import { useState, useEffect } from "react";

export const useOptimizedLoading = () => {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Disable scrolling during loading
    document.body.style.overflow = 'hidden';

    // Simple loading timer - just show the creative animation for a set duration
    const loadingTimer = setTimeout(() => {
      setIsLoading(false);
      // Re-enable scrolling
      document.body.style.overflow = 'unset';
    }, 3000); // 3 seconds to enjoy the creative animation

    // Cleanup function
    return () => {
      clearTimeout(loadingTimer);
      document.body.style.overflow = 'unset';
    };
  }, []);

  return {
    isLoading,
  };
};
