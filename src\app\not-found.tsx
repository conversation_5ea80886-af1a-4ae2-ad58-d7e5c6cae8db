import Link from 'next/link';
import { Home, Search } from 'lucide-react';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: '404 - Page Not Found | TregoTech',
  description: 'The page you are looking for could not be found. Return to TregoTech homepage to explore our technology solutions and services.',
  robots: {
    index: false,
    follow: true,
  },
};

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0a0a0a] via-[#1a1a2e] to-[#16213e] flex items-center justify-center px-6">
      <div className="max-w-4xl mx-auto text-center">
        
        {/* 404 Header */}
        <div className="mb-8">
          <h1 className="text-8xl md:text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-blue-400 font-orbitron mb-4">
            404
          </h1>
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4 font-orbitron">
            Page Not Found
          </h2>
          <p className="text-lg text-gray-300 font-sora max-w-2xl mx-auto leading-relaxed">
            The page you&apos;re looking for doesn&apos;t exist or has been moved.
            Let&apos;s get you back to exploring our innovative technology solutions.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
          <Link
            href="/"
            className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-xl hover:from-purple-700 hover:to-blue-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl font-sora"
          >
            <Home className="w-5 h-5 mr-2" />
            Back to Home
          </Link>
          
          <Link
            href="/about"
            className="inline-flex items-center px-8 py-4 bg-transparent border-2 border-purple-400 text-purple-400 font-semibold rounded-xl hover:bg-purple-400 hover:text-white transition-all duration-300 transform hover:scale-105 font-sora"
          >
            <Search className="w-5 h-5 mr-2" />
            Learn About Us
          </Link>
        </div>

        {/* Helpful Links */}
        <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 mb-8">
          <h3 className="text-xl font-bold text-white mb-6 font-orbitron">
            Popular Pages
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link
              href="/about"
              className="p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-colors duration-300 group"
            >
              <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300 font-sora">
                About Us
              </h4>
              <p className="text-gray-400 text-sm">
                Learn about our mission and vision
              </p>
            </Link>
            
            <Link
              href="/contact"
              className="p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-colors duration-300 group"
            >
              <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300 font-sora">
                Contact Us
              </h4>
              <p className="text-gray-400 text-sm">
                Get in touch with our team
              </p>
            </Link>
            
            <Link
              href="/"
              className="p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-colors duration-300 group"
            >
              <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300 font-sora">
                Our Services
              </h4>
              <p className="text-gray-400 text-sm">
                Explore our technology solutions
              </p>
            </Link>
          </div>
        </div>

        {/* Contact Information */}
        <div className="text-center">
          <p className="text-gray-400 font-sora mb-2">
            Need help? Contact us at{' '}
            <a 
              href="mailto:<EMAIL>" 
              className="text-purple-400 hover:text-purple-300 transition-colors duration-200"
            >
              <EMAIL>
            </a>
          </p>
          <p className="text-gray-500 text-sm font-sora">
            © 2024 TregoTech. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  );
}
