"use client";

import { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Register ScrollTrigger plugin
if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

export default function OptimizedBackgroundEffects() {
  const containerRef = useRef<HTMLDivElement>(null);
  const waveRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;
    const currentWave = waveRef.current;
    if (!container) return;

    // Create subtle morphing wave effect with better performance
    const createOptimizedWave = () => {
      if (!waveRef.current) return;

      ScrollTrigger.create({
        trigger: "body",
        start: "top top",
        end: "bottom bottom",
        scrub: 3, // Even slower for smoother transitions
        onUpdate: (self) => {
          const progress = self.progress;

          // Check if we're in the mobile app section and reduce wave intensity
          const mobileAppSection = document.getElementById("mobile-app-showcase");
          const isInMobileSection = mobileAppSection &&
            window.scrollY >= mobileAppSection.offsetTop - window.innerHeight / 2 &&
            window.scrollY <= mobileAppSection.offsetTop + mobileAppSection.offsetHeight;

          const waveIntensity = isInMobileSection ? 0.3 : 1; // Reduce intensity in mobile section

          // Simplified wave animation with fewer calculations
          gsap.set(waveRef.current, {
            background: `radial-gradient(circle at ${50 + Math.sin(progress * Math.PI * 2) * 10 * waveIntensity}% ${50 + Math.cos(progress * Math.PI * 2) * 10 * waveIntensity}%,
              rgba(186, 66, 255, ${(0.04 + progress * 0.06) * waveIntensity}) 0%,
              rgba(0, 225, 255, ${(0.02 + progress * 0.04) * waveIntensity}) 50%,
              transparent 70%)`,
            transform: `scale(${1 + progress * 0.3 * waveIntensity}) rotate(${progress * 45 * waveIntensity}deg)`,
          });
        },
      });
    };

    // Create minimal floating particles (much fewer than before)
    const createOptimizedParticles = () => {
      const particleCount = 8; // Reduced from 50+ to 8

      for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement("div");
        particle.className = "background-particle"; // Add class for easier targeting
        particle.style.cssText = `
          position: fixed;
          width: ${Math.random() * 4 + 2}px;
          height: ${Math.random() * 4 + 2}px;
          background: ${i % 2 === 0 ? '#BA42FF' : '#00E1FF'};
          border-radius: 50%;
          left: ${Math.random() * 100}%;
          top: ${Math.random() * 100}%;
          opacity: 0.4;
          pointer-events: none;
          will-change: transform;
          transition: background-color 0.5s ease;
        `;
        container.appendChild(particle);

        // Slower, more subtle animation with color change detection
        gsap.to(particle, {
          y: `random(-50, 50)`,
          x: `random(-50, 50)`,
          duration: `random(8, 15)`, // Slower animation
          repeat: -1,
          yoyo: true,
          ease: "sine.inOut",
        });

        // Add scroll-triggered color change for mobile app section
        ScrollTrigger.create({
          trigger: "#mobile-app-showcase",
          start: "top 95%",
          end: "bottom 5%",
          onEnter: () => {
            particle.style.background = '#FFFFFF';
            particle.style.opacity = '0.6';
          },
          onLeave: () => {
            particle.style.background = i % 2 === 0 ? '#302cff' : '#00E1FF';
            particle.style.opacity = '0.4';
          },
          onEnterBack: () => {
            particle.style.background = '#FFFFFF';
            particle.style.opacity = '0.6';
          },
          onLeaveBack: () => {
            particle.style.background = i % 2 === 0 ? '#302cff' : '#00E1FF';
            particle.style.opacity = '0.4';
          }
        });
      }
    };

    // Create subtle energy pulses (fewer and slower)
    const createOptimizedPulses = () => {
      const pulseCount = 3; // Reduced from 5 to 3

      for (let i = 0; i < pulseCount; i++) {
        const pulse = document.createElement("div");
        pulse.className = "background-pulse"; // Add class for easier targeting
        const originalColor = i % 2 === 0 ? '#302cff' : '#00E1FF';
        pulse.style.cssText = `
          position: fixed;
          width: 150px;
          height: 150px;
          border: 1px solid ${originalColor};
          border-radius: 50%;
          opacity: 0;
          pointer-events: none;
          z-index: 1;
          will-change: transform;
          transition: border-color 0.5s ease;
        `;
        container.appendChild(pulse);

        // Add scroll-triggered color change for mobile app section
        ScrollTrigger.create({
          trigger: "#mobile-app-showcase",
          start: "top 95%",
          end: "bottom 5%",
          onEnter: () => {
            pulse.style.borderColor = '#FFFFFF';
          },
          onLeave: () => {
            pulse.style.borderColor = originalColor;
          },
          onEnterBack: () => {
            pulse.style.borderColor = '#FFFFFF';
          },
          onLeaveBack: () => {
            pulse.style.borderColor = originalColor;
          }
        });

        ScrollTrigger.create({
          trigger: "body",
          start: "top top",
          end: "bottom bottom",
          scrub: 3, // Even slower scrub for better performance
          onUpdate: (self) => {
            const progress = self.progress;
            gsap.set(pulse, {
              left: `${30 + i * 20}%`,
              top: `${40 + Math.sin(progress * Math.PI + i) * 20}%`,
              scale: 0.8 + progress * 1.2,
              opacity: Math.sin(progress * Math.PI * 2 + i) * 0.3 + 0.2,
              rotation: progress * 180,
            });
          },
        });
      }
    };

    createOptimizedWave();
    createOptimizedParticles();
    createOptimizedPulses();

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
      // Clean up particles
      const particles = container.querySelectorAll('div');
      particles.forEach(particle => {
        if (particle !== currentWave && particle.parentNode === container) {
          container.removeChild(particle);
        }
      });
    };
  }, []);

  return (
    <div ref={containerRef} className="fixed inset-0 pointer-events-none overflow-hidden z-0">
      {/* Optimized Wave Background */}
      <div
        ref={waveRef}
        className="absolute inset-0 w-full h-full"
        style={{
          background: "radial-gradient(circle at 50% 50%, rgba(186, 66, 255, 0.08) 0%, rgba(0, 225, 255, 0.04) 50%, transparent 70%)",
        }}
      />
      
      {/* Static gradient base - no animation for better performance */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#0a0a0a] via-[#1a1a2e] to-[#16213e] opacity-95" />
      
      {/* Subtle static grid overlay - no animation */}
      <div
        className="absolute inset-0 opacity-5"
        style={{
          backgroundImage: `
            linear-gradient(rgba(186, 66, 255, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(186, 66, 255, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: "80px 80px",
        }}
      />
    </div>
  );
}
