"use client";

import { useRouter } from "next/navigation";

interface ContactButtonProps {
  children?: React.ReactNode;
  className?: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
}

export default function ContactButton({
  children = "Contact Us",
  className = "",
  variant = 'primary',
  size = 'md'
}: ContactButtonProps) {
  const router = useRouter();

  const baseClasses = "inline-flex items-center justify-center font-bold transition-all duration-300 rounded-xl font-orbitron";
  
  const variantClasses = {
    primary: "bg-gradient-to-r from-[#302cff] to-[#1e1a99] hover:from-[#1e1a99] hover:to-[#0f0d66] text-white hover:scale-105 hover:shadow-lg hover:shadow-[#302cff]/30",
    secondary: "bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white hover:scale-105",
    outline: "border-2 border-[#302cff] text-[#302cff] hover:bg-[#302cff] hover:text-white hover:scale-105",
    ghost: "text-[#302cff] hover:text-[#4d42ff] hover:bg-[#302cff]/10 hover:scale-105"
  };

  const sizeClasses = {
    sm: "px-4 py-2 text-sm",
    md: "px-6 py-3 text-base",
    lg: "px-8 py-4 text-lg"
  };

  const buttonClasses = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;

  const handleClick = () => {
    router.push('/contact');
  };

  return (
    <button
      onClick={handleClick}
      className={buttonClasses}
    >
      {children}
    </button>
  );
}
