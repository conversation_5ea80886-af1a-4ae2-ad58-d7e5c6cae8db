// // apiClient.ts

import axios, { AxiosInstance } from "axios";

const apiClient: AxiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

let refreshTokenPromise: Promise<unknown> | null = null;

apiClient.interceptors.request.use(
  (config) => {
    if (config.headers?.Authorization) {
      return config; // If Authorization header already exists, return config
    }

    if (typeof window !== "undefined") {
      const authData = localStorage.getItem("authData");
      if (authData) {
        try {
          const parsedData = JSON.parse(authData);
          const accessToken = parsedData?.accessToken;
          if (accessToken) {
            config.headers.Authorization = `Bearer ${accessToken}`;
            console.log("Authorization header added:", config.headers);  // Debug this
          } else {
            console.log("No accessToken found in authData");
          }
        } catch (e) {
          console.error("Failed to parse authData from localStorage:", e);
        }
      } else {
        console.log("No authData found in localStorage");
      }
    }

    return config;
  },
  (error) => Promise.reject(error)
);


// Refresh token logic in the response interceptor
apiClient.interceptors.response.use(
  (response) => response.data,
  async (error) => {
    console.log("API Error:", error);

    const status = error.response?.status;
    const errorMessage =
      error.response?.data?.message || error.message || "Something went wrong";

    const originalRequest = error.config;

    if (status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      // Check if running server-side
      if (typeof window === "undefined") {
        console.error("Cannot refresh token on server-side");
        return Promise.reject(new Error("Cannot refresh token on server-side"));
      }

      // Client-side refresh token logic
      try {
        if (!refreshTokenPromise) {
          let authData: string | null = null;
          let refreshToken: string | null = null;

          authData = localStorage.getItem("authData") || null;
          console.log("authData:", authData);
          if (authData) {
            try {
              const parsedData = JSON.parse(authData);
              refreshToken = parsedData.refreshToken || null;
              if (!refreshToken) {
                console.error("refreshToken is missing in parsed authData:", parsedData);
              }
            } catch (e) {
              console.error("Failed to parse authData from localStorage:", e, "Raw authData:", authData);
            }
          } else {
            console.error("No authData found in localStorage");
          }

          if (!refreshToken) {
            console.error("No refresh token available");
            return Promise.reject(new Error("No refresh token available"));
          }

          // Start refresh token request
          refreshTokenPromise = apiClient.post("/auth/api/refresh-token", {
            refreshToken,
          });

          const response = await refreshTokenPromise as {
            data?: { accessToken: string; refreshToken: string };
            accessToken?: string;
            refreshToken?: string;
            accessTokenExpiresAt?: number;
            refreshTokenExpiresAt?: number;
          };
          const { accessToken, refreshToken: newRefreshToken } = response.data || response;

          // Update localStorage with new tokens
          localStorage.setItem(
            "authData",
            JSON.stringify({
              accessToken,
              refreshToken: newRefreshToken || refreshToken,
              accessTokenExpiresAt: response.accessTokenExpiresAt || 86400,
              refreshTokenExpiresAt: response.refreshTokenExpiresAt || 7776000,
            })
          );

          // Update original request with new access token
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;

          // Clear refresh token promise
          refreshTokenPromise = null;

          // Retry original request
          return apiClient(originalRequest);
        }

        // If refresh token request is already in progress, wait for it
        await refreshTokenPromise;
        const authData = localStorage.getItem("authData");
        if (authData) {
          const { accessToken } = JSON.parse(authData);
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          refreshTokenPromise = null;
          return apiClient(originalRequest);
        }
      } catch (e) {
        console.error("Failed to refresh access token:", e);
        refreshTokenPromise = null;
        localStorage.removeItem("authData");
        window.location.href = "/login";
        return Promise.reject(new Error("Session expired, please log in again"));
      }
    }

    console.error("API Client Error:", errorMessage);
    return Promise.reject(new Error(errorMessage));
  }
);

async function post<T>(url: string, data: unknown, config: Record<string, unknown> = {}, accessToken?: string): Promise<T> {
  if (accessToken) {
    config.headers = {
      ...(config.headers as Record<string, unknown> || {}),
      Authorization: `Bearer ${accessToken}`
    };
  }
  return apiClient.post(url, data, config);
}

async function get<T>(url: string, config: Record<string, unknown> = {}, accessToken?: string): Promise<T> {
  if (accessToken) {
    config.headers = {
      ...(config.headers as Record<string, unknown> || {}),
      Authorization: `Bearer ${accessToken}`
    };
  }
  return apiClient.get(url, config);
}

const apiClientExports = {
  post,
  get,
};

export default apiClientExports;
