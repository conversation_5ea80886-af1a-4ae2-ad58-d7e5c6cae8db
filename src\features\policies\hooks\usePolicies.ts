"use client";

import { useState } from "react";
import { PolicyData } from "@/types/policy";
import { usePolicies as useOriginalPolicies } from "@/hooks/usePolicies";

export const usePolicies = () => {
  const { policies, loading, error, refetch } = useOriginalPolicies();
  const [language, setLanguage] = useState<'en' | 'ar'>('en');

  const getTitle = (policy: PolicyData) => {
    return language === 'en' ? policy.title_en : policy.title_ar;
  };

  const getSubtitle = (policy: PolicyData) => {
    return language === 'en' ? policy.subtitle_en : policy.subtitle_ar;
  };

  const getDescription = (policy: PolicyData) => {
    return language === 'en' ? policy.description_en : policy.description_ar;
  };

  return {
    policies,
    loading,
    error,
    refetch,
    language,
    setLanguage,
    getTitle,
    getSubtitle,
    getDescription
  };
};
