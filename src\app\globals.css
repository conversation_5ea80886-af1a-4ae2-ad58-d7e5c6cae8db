@import "tailwindcss";

/* PERFORMANCE OPTIMIZED: Font loading with fallbacks */
@font-face {
  font-family: 'Orbitron';
  src: url('/fonts/Orbitron-Regular.woff2') format('woff2');
  font-weight: 400 700;
  font-display: optional; /* Better for LCP - shows fallback immediately */
}

@font-face {
  font-family: 'Sora';
  src: url('/fonts/Sora-Regular.woff2') format('woff2');
  font-weight: 400 700;
  font-display: optional; /* Better for LCP - shows fallback immediately */
}

@font-face {
  font-family: 'Space Grotesk';
  src: url('/fonts/SpaceGrotesk-Regular.woff2') format('woff2');
  font-weight: 400 700;
  font-display: optional; /* Better for LCP - shows fallback immediately */
}

/* PERFORMANCE OPTIMIZED: Font helper classes with system fallbacks */
.font-orbitron {
  font-family: 'Orbitron', 'Arial Black', 'Helvetica Neue', Arial, sans-serif;
}

.font-sora {
  font-family: 'Sora', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.font-space {
  font-family: 'Space Grotesk', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}


:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: transparent;
  color: #000000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
}

/* CRITICAL: Above-the-fold optimizations for LCP */
.critical-content {
  contain: layout style paint;
  will-change: auto;
}

/* PERFORMANCE: Reduce paint complexity */
.gpu-optimized {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Custom animations for Landing component */
@keyframes spin-smooth {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-smooth {
  animation: spin-smooth 2s linear infinite;
}

.animate-spin-slow {
  animation: spin-slow 20s linear infinite;
}

/* Optimized Performance Animations */
@keyframes optimized-pulse {
  0%, 100% {
    opacity: 0.4;
  }
  50% {
    opacity: 0.8;
  }
}

/* Futuristic Cursor Effects */
.cursor-hover-effect {
  transition: transform 0.2s ease-out;
  will-change: transform;
}

.cursor-hover-effect:hover {
  transform: scale(1.02);
}

/* Ensure custom cursor is always visible */
* {
  cursor: none !important;
}

/* Force cursor visibility on all interactive elements */
button, a, input, textarea, [data-cursor="pointer"], .cursor-hover-effect {
  cursor: none !important;
}

/* Prevent any element from blocking the cursor */
.futuristic-cursor {
  pointer-events: none !important;
  z-index: 999999 !important;
  position: fixed !important;
}

/* Zigzag Timeline Animations */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-100px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* Optimized: Simplified float animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* Removed dash animation for better performance */

/* Timeline hover effects */
.timeline-card:hover {
  transform: scale(1.05) rotate(1deg);
  box-shadow: 0 25px 50px rgba(186, 66, 255, 0.3);
}

/* Performance optimized animations */
.animate-optimized-pulse {
  animation: optimized-pulse 2s ease-in-out infinite;
  will-change: opacity;
}

/* Image optimization */
img {
  content-visibility: auto;
}

/* EXTREME PERFORMANCE MODE - DISABLE CONTINUOUS ANIMATIONS */
.animate-bounce,
.animate-spin,
.animate-pulse,
.animate-float-small,
.animate-float-1,
.animate-float-2,
.animate-float-3,
.animate-gradient,
.animate-gradient-x,
.animate-spin-slow,
.animate-spin-reverse,
.animate-bounce-slow,
.animate-glow-pulse {
  animation: none !important;
}

/* Keep only essential transitions */
* {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
}

/* Allow only opacity and transform transitions for performance */
button, .transition-all, .transition-transform, .transition-colors, .transition-opacity {
  transition-property: opacity, transform, color, background-color !important;
  transition-duration: 0.3s !important;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Hide scrollbar but keep functionality */
* {
  scrollbar-width: thin;
  scrollbar-color: #1e3a8a #1a1a2e;
}

*::-webkit-scrollbar {
  width: 8px;
}

*::-webkit-scrollbar-track {
  background: #1a1a2e;
}

*::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #1e3a8a, #1e40af);
  border-radius: 4px;
}

*::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #1e40af, #1e3a8a);
}

/* Service Cards Enhanced Styles */
.service-card-glow {
  position: relative;
  overflow: hidden;
}

.service-card-glow::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #302cff, #00E1FF, #302cff);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.service-card-glow:hover::before {
  opacity: 1;
}

/* Gradient text animation */
@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.animate-gradient {
  background: linear-gradient(-45deg, #BA42FF, #00E1FF, #BA42FF, #00E1FF);
  background-size: 400% 400%;
  animation: gradient-shift 3s ease infinite;
}

/* Advanced Service Card Animations */
.perspective-1000 {
  perspective: 1000px;
}

.transform-style-preserve-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

.rotateY-12 {
  transform: rotateY(12deg);
}

/* Small phone floating animation */
/* @keyframes float-small {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-5px) rotate(1deg);
  }
} */

.animate-float-small {
  animation: float-small 4s ease-in-out infinite;
}

/* Enhanced fade in up animation for services */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* Cool service card entrance animation */
@keyframes serviceCardEntrance {
  0% {
    opacity: 0;
    transform: translateY(60px) rotateX(15deg) scale(0.8);
  }
  50% {
    opacity: 0.7;
    transform: translateY(20px) rotateX(5deg) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) rotateX(0deg) scale(1);
  }
}

.service-card.animate-fade-in-up {
  animation: serviceCardEntrance 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* Staggered animation delays */
.service-card:nth-child(1) { animation-delay: 0ms; }
.service-card:nth-child(2) { animation-delay: 150ms; }
.service-card:nth-child(3) { animation-delay: 300ms; }
.service-card:nth-child(4) { animation-delay: 450ms; }
.service-card:nth-child(5) { animation-delay: 600ms; }
.service-card:nth-child(6) { animation-delay: 750ms; }

/* Gradient border animation */
@keyframes gradientBorder {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.border-gradient-animated {
  background: linear-gradient(-45deg, #302cff, #00E1FF, #302cff, #00E1FF);
  background-size: 400% 400%;
  animation: gradientBorder 3s ease infinite;
}

/* New Modern Services Animations */
.service-item {
  opacity: 0;
  transform: translateY(50px) scale(0.95);
}

/* Slide in animation for service items */
@keyframes slideIn {
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.service-item.animate-slide-in {
  animation: slideIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* Enhanced hover effects for service cards */
.service-item:hover {
  transform: translateY(-8px) scale(1.02);
}

/* Floating animation for particles */
@keyframes floatUp {
  0% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: translateY(-20px) rotate(360deg);
    opacity: 0.3;
  }
}

.animate-float-up {
  animation: floatUp 3s ease-in-out infinite;
}

/* Landing Page Entrance Animations */
.landing-element {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.landing-element.animate-entrance {
  opacity: 1;
  transform: translateY(0px);
}

/* Services Section - Optimized Animations */
.service-card {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.service-card.animate-fade-in-up {
  opacity: 1;
  transform: translateY(0);
}

/* Service card staggered entrance */
.service-card:nth-child(1) {
  transition-delay: 0ms;
}

.service-card:nth-child(2) {
  transition-delay: 150ms;
}

.service-card:nth-child(3) {
  transition-delay: 300ms;
}

.service-card:nth-child(4) {
  transition-delay: 450ms;
}

.service-card:nth-child(5) {
  transition-delay: 600ms;
}

.service-card:nth-child(6) {
  transition-delay: 750ms;
}

/* Mobile App Section Entrance Animations */
.mobile-element {
  opacity: 0;
  transform: translateY(50px);
  transition: all 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.mobile-element.animate-entrance {
  opacity: 1;
  transform: translateY(0px);
}

.mobile-feature-card {
  opacity: 0;
  transform: translateY(30px) scale(0.95);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.mobile-feature-card.animate-entrance {
  opacity: 1;
  transform: translateY(0px) scale(1);
}

.mobile-bottom-card {
  opacity: 0;
  transform: translateY(40px);
  transition: all 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.mobile-bottom-card.animate-entrance {
  opacity: 1;
  transform: translateY(0px);
}

/* Mobile Screenshots - GSAP Animations */
.mobile-screen-main {
  opacity: 0;
  transform: translateY(80px) scale(0.7);
}

.mobile-screen-additional {
  opacity: 0;
  transform: translateY(60px) scale(0.6);
}

/* Travel Options Cards Entrance Animations */
.travel-option-card {
  opacity: 0;
  transform: translateY(50px) scale(0.95);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.travel-option-card.animate-entrance {
  opacity: 1;
  transform: translateY(0px) scale(1);
}

/* BECOME PARTNER SECTION ANIMATIONS */
.partner-element {
  opacity: 0;
  transform: translateY(40px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.partner-element.partner-animate-in {
  opacity: 1;
  transform: translateY(0);
}

.partner-benefit-item {
  opacity: 0;
  transform: translateX(-30px);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.partner-benefit-item.partner-animate-in {
  opacity: 1;
  transform: translateX(0);
}

.partner-stat-item {
  opacity: 0;
  transform: translateY(30px) scale(0.9);
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.partner-stat-item.partner-animate-in {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* Landing page animations */
@keyframes gradient-x {
  0%, 100% {
    background-size: 200% 200%;
    background-position: left center;
  }
  50% {
    background-size: 200% 200%;
    background-position: right center;
  }
}

.animate-gradient-x {
  animation: gradient-x 3s ease infinite;
}

@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow 8s linear infinite;
}

@keyframes spin-reverse {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}

.animate-spin-reverse {
  animation: spin-reverse 6s linear infinite;
}

@keyframes bounce-slow {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-bounce-slow {
  animation: bounce-slow 2s ease-in-out infinite;
}

/* ULTRA-SIMPLE BACKGROUND - ZERO ANIMATIONS FOR PERFECT PERFORMANCE */

/* Simple float animation for compatibility only */
@keyframes float-simple {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float-simple {
  animation: float-simple 6s ease-in-out infinite;
}

/* MINIMAL LANDING PAGE ANIMATIONS - ULTRA PERFORMANCE */

/* Simple gradient animation */
@keyframes gradient-x {
  0%, 100% {
    background-size: 200% 200%;
    background-position: left center;
  }
  50% {
    background-size: 200% 200%;
    background-position: right center;
  }
}

.animate-gradient-x {
  animation: gradient-x 4s ease infinite;
}

/* MODERN SERVICE ANIMATIONS - PERFORMANCE FOCUSED */

/* Enhanced service item transitions */
.service-item {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Smooth glow effect on hover */
.service-item:hover {
  filter: drop-shadow(0 20px 40px rgba(186, 66, 255, 0.15));
}

/* Optimized: Simplified floating particles */
@keyframes float-1 {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-8px); }
}

@keyframes float-2 {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes float-3 {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-8px) scale(1.2); }
}

.animate-float-1 {
  animation: float-1 4s ease-in-out infinite;
}

.animate-float-2 {
  animation: float-2 3s ease-in-out infinite 0.5s;
}

.animate-float-3 {
  animation: float-3 3.5s ease-in-out infinite 1s;
}

/* Subtle Bounce */
@keyframes bounce-subtle {
  0%, 100% { transform: translateY(0px) scale(1.25) rotate(12deg); }
  50% { transform: translateY(-5px) scale(1.3) rotate(15deg); }
}

.animate-bounce-subtle {
  animation: bounce-subtle 2s ease-in-out infinite;
}

/* Pulse Glow */
@keyframes pulse-glow {
  0%, 100% { filter: drop-shadow(0 0 5px rgba(186, 66, 255, 0.3)); }
  50% { filter: drop-shadow(0 0 20px rgba(186, 66, 255, 0.8)) drop-shadow(0 0 30px rgba(0, 225, 255, 0.4)); }
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Slow Spin */
@keyframes spin-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-spin-slow {
  animation: spin-slow 8s linear infinite;
}

/* Additional 3D Transform Utilities */
.transform-gpu {
  transform: translate3d(0, 0, 0);
}

/* AMAZING Service Card - GSAP Controlled (no CSS hover conflicts) */
.service-card-container {
  will-change: auto;
  transform-style: preserve-3d;
}

.service-icon {
  will-change: auto;
}

/* Removed 3D CSS properties for better performance */

/* Enhanced Glow Effects */
@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(48, 44, 255, 0.3),
                0 0 40px rgba(0, 225, 255, 0.2),
                inset 0 0 20px rgba(255, 255, 255, 0.1);
  }
  50% {
    box-shadow: 0 0 40px rgba(48, 44, 255, 0.6),
                0 0 80px rgba(0, 225, 255, 0.4),
                inset 0 0 30px rgba(255, 255, 255, 0.2);
  }
}

.animate-glow-pulse {
  animation: glow-pulse 3s ease-in-out infinite;
}

/* Morphing Background */
@keyframes morph-bg {
  0%, 100% { border-radius: 24px; }
  25% { border-radius: 32px 24px 32px 24px; }
  50% { border-radius: 40px; }
  75% { border-radius: 24px 32px 24px 32px; }
}

.animate-morph {
  animation: morph-bg 8s ease-in-out infinite;
}

/* SMOOTH SERVICES ANIMATIONS - PERFORMANCE OPTIMIZED */
.services-element {
  opacity: 0;
  transform: translateY(40px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.services-element.services-animate-in {
  opacity: 1;
  transform: translateY(0);
}

.services-card-element {
  opacity: 0;
  transform: translateY(50px) scale(0.95);
  transition: all 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.services-card-element.services-animate-in {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* TRAVEL OPTIONS EXPANDABLE ANIMATIONS */
.travel-option-container {
  transition: all 0.3s ease-in-out;
}

.travel-option-card {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
}

.travel-option-card:hover {
  transform: translateY(-4px) scale(1.02);
}

.travel-option-expanded {
  animation: expandIn 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes expandIn {
  0% {
    max-height: 0;
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    max-height: 800px;
    opacity: 1;
    transform: translateY(0);
  }
}

.travel-option-details {
  animation: slideInUp 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s both;
}

@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Navbar dropdown animation */
@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slideInUp {
  animation: slideInUp 0.3s ease-out forwards;
}

/* Enhanced spinner animations */
@keyframes spin-reverse {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}

@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-reverse {
  animation: spin-reverse 2s linear infinite;
}

.animate-spin-slow {
  animation: spin-slow 4s linear infinite;
}

/* Enhanced loading text animations */
@keyframes loading-dots {
  0%, 20% {
    opacity: 0;
    transform: translateY(0);
  }
  50% {
    opacity: 1;
    transform: translateY(-10px);
  }
  80%, 100% {
    opacity: 0;
    transform: translateY(0);
  }
}

.animate-loading-dots {
  animation: loading-dots 1.5s ease-in-out infinite;
}

/* Enhanced Loading Screen Animations */
@keyframes letter-wave {
  0%, 100% {
    transform: translateY(0px) scale(1);
    color: #ffffff;
  }
  50% {
    transform: translateY(-8px) scale(1.1);
    color: #BA42FF;
    text-shadow: 0 0 20px rgba(186, 66, 255, 0.8);
  }
}

@keyframes text-glow {
  0%, 100% {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  }
  50% {
    text-shadow: 0 0 20px rgba(186, 66, 255, 0.8), 0 0 30px rgba(0, 225, 255, 0.6);
  }
}

@keyframes dot-bounce {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-12px) scale(1.3);
    opacity: 1;
    color: #00E1FF;
    text-shadow: 0 0 15px rgba(0, 225, 255, 0.8);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(400%);
  }
}

@keyframes progress-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(186, 66, 255, 0.6), 0 0 40px rgba(0, 225, 255, 0.4);
  }
  50% {
    box-shadow: 0 0 30px rgba(186, 66, 255, 0.8), 0 0 60px rgba(0, 225, 255, 0.6);
  }
}

@keyframes progress-light {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(300%);
  }
}

@keyframes particle-float-1 {
  0%, 100% {
    transform: translateX(0px) translateY(0px);
    opacity: 0;
  }
  25% {
    opacity: 1;
  }
  50% {
    transform: translateX(100px) translateY(-10px);
    opacity: 0.8;
  }
  75% {
    opacity: 0.4;
  }
}

@keyframes particle-float-2 {
  0%, 100% {
    transform: translateX(0px) translateY(0px);
    opacity: 0;
  }
  30% {
    opacity: 1;
  }
  60% {
    transform: translateX(150px) translateY(-8px);
    opacity: 0.6;
  }
  80% {
    opacity: 0.2;
  }
}

@keyframes particle-float-3 {
  0%, 100% {
    transform: translateX(0px) translateY(0px);
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  70% {
    transform: translateX(120px) translateY(-12px);
    opacity: 0.7;
  }
  90% {
    opacity: 0.1;
  }
}

.animate-letter-wave {
  animation: letter-wave 2s ease-in-out infinite;
}

.animate-text-glow {
  animation: text-glow 3s ease-in-out infinite;
}

.animate-dot-bounce {
  animation: dot-bounce 1.5s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 2s ease-in-out infinite;
}

.animate-progress-glow {
  animation: progress-glow 2s ease-in-out infinite;
}

.animate-progress-light {
  animation: progress-light 3s ease-in-out infinite;
}

.animate-particle-1 {
  animation: particle-float-1 4s ease-in-out infinite;
}

.animate-particle-2 {
  animation: particle-float-2 3.5s ease-in-out infinite 0.5s;
}

.animate-particle-3 {
  animation: particle-float-3 4.5s ease-in-out infinite 1s;
}

/* Enhanced Spinner Animations */
@keyframes spinner-entrance {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(0deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(360deg);
  }
}

@keyframes ring-glow {
  0%, 100% {
    box-shadow: 0 0 40px rgba(186, 66, 255, 0.8);
  }
  50% {
    box-shadow: 0 0 60px rgba(186, 66, 255, 1), 0 0 80px rgba(139, 92, 246, 0.6);
  }
}

@keyframes spin-reverse-fast {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}

@keyframes core-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 50px rgba(139, 92, 246, 1);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 70px rgba(139, 92, 246, 1), 0 0 90px rgba(186, 66, 255, 0.8);
  }
}

@keyframes inner-glow {
  0%, 100% {
    box-shadow: inset 0 0 20px rgba(186, 66, 255, 0.5);
  }
  50% {
    box-shadow: inset 0 0 30px rgba(0, 225, 255, 0.7);
  }
}

@keyframes orbit-fast {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes dot-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.3);
    opacity: 1;
  }
}

@keyframes wave-1 {
  0% {
    transform: rotate(0deg) scale(1);
    opacity: 0.3;
  }
  50% {
    opacity: 0.1;
  }
  100% {
    transform: rotate(360deg) scale(1.1);
    opacity: 0.3;
  }
}

@keyframes wave-2 {
  0% {
    transform: rotate(0deg) scale(1);
    opacity: 0.2;
  }
  50% {
    opacity: 0.05;
  }
  100% {
    transform: rotate(-360deg) scale(1.05);
    opacity: 0.2;
  }
}

.animate-spinner-entrance {
  animation: spinner-entrance 1s ease-out;
}

.animate-ring-glow {
  animation: ring-glow 2s ease-in-out infinite;
}

.animate-spin-reverse-fast {
  animation: spin-reverse-fast 1.5s linear infinite;
}

.animate-core-pulse {
  animation: core-pulse 2s ease-in-out infinite;
}

.animate-inner-glow {
  animation: inner-glow 3s ease-in-out infinite;
}

.animate-orbit-fast {
  animation: orbit-fast 3s linear infinite;
}

.animate-dot-pulse {
  animation: dot-pulse 1.5s ease-in-out infinite;
}

.animate-wave-1 {
  animation: wave-1 4s linear infinite;
}

.animate-wave-2 {
  animation: wave-2 5s linear infinite reverse;
}

/* Optimized Loading Screen Animations */
@keyframes logo-entrance {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes logo-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(48, 44, 255, 0.3), 0 0 40px rgba(186, 66, 255, 0.2);
  }
  50% {
    box-shadow: 0 0 30px rgba(48, 44, 255, 0.5), 0 0 60px rgba(186, 66, 255, 0.3);
  }
}

@keyframes text-shimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

@keyframes brand-pulse {
  0%, 100% {
    color: #302cff;
    text-shadow: 0 0 10px rgba(48, 44, 255, 0.5);
  }
  50% {
    color: #BA42FF;
    text-shadow: 0 0 20px rgba(186, 66, 255, 0.8);
  }
}

@keyframes line-expand {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 4rem;
    opacity: 1;
  }
}

@keyframes logo-float {
  0%, 100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-8px) scale(1.02);
  }
}

@keyframes line-pulse {
  0%, 100% {
    box-shadow: 0 0 5px rgba(186, 66, 255, 0.5);
    transform: scaleX(1);
  }
  50% {
    box-shadow: 0 0 15px rgba(186, 66, 255, 0.8), 0 0 25px rgba(0, 225, 255, 0.6);
    transform: scaleX(1.1);
  }
}

@keyframes stage-text {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes progress-entrance {
  0% {
    opacity: 0;
    transform: translateY(15px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer-fast {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(300%);
  }
}

@keyframes progress-light-fast {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(200%);
  }
}

@keyframes percentage-update {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes particle-float-1 {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) translateX(10px);
    opacity: 1;
  }
}

@keyframes particle-float-2 {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.4;
  }
  50% {
    transform: translateY(-15px) translateX(-8px);
    opacity: 1;
  }
}

@keyframes particle-float-3 {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.2;
  }
  50% {
    transform: translateY(-25px) translateX(5px);
    opacity: 0.8;
  }
}

@keyframes text-fade-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.02);
  }
}

@keyframes progress-glow-continuous {
  0%, 100% {
    box-shadow: 0 0 10px rgba(186, 66, 255, 0.4);
  }
  50% {
    box-shadow: 0 0 20px rgba(186, 66, 255, 0.8), 0 0 30px rgba(0, 225, 255, 0.6);
  }
}

@keyframes percentage-pulse {
  0%, 100% {
    color: #9ca3af;
    transform: scale(1);
  }
  50% {
    color: #BA42FF;
    transform: scale(1.05);
  }
}

.animate-logo-entrance {
  animation: logo-entrance 1s ease-out, logo-float 4s ease-in-out 1s infinite;
}

.animate-logo-glow {
  animation: logo-glow 3s ease-in-out infinite;
}

.animate-text-shimmer {
  background: linear-gradient(90deg, #ffffff, #BA42FF, #ffffff);
  background-size: 200% 100%;
  -webkit-background-clip: text;
  background-clip: text;
  animation: text-shimmer 2s ease-in-out infinite;
}

/* Creative Loading Screen Animations */
@keyframes float-1 {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.6;
  }
  33% {
    transform: translateY(-20px) translateX(10px);
    opacity: 1;
  }
  66% {
    transform: translateY(10px) translateX(-5px);
    opacity: 0.8;
  }
}

@keyframes float-2 {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-15px) translateX(-8px);
    opacity: 1;
  }
}

@keyframes float-3 {
  0%, 100% {
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0.4;
  }
  25% {
    transform: translateY(-10px) translateX(15px) scale(1.2);
    opacity: 0.7;
  }
  75% {
    transform: translateY(5px) translateX(-10px) scale(0.8);
    opacity: 0.6;
  }
}

@keyframes float-4 {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.7;
  }
  40% {
    transform: translateY(-25px) translateX(-12px);
    opacity: 1;
  }
  80% {
    transform: translateY(8px) translateX(6px);
    opacity: 0.5;
  }
}

@keyframes float-5 {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.9;
  }
  60% {
    transform: translateY(-18px) translateX(20px);
    opacity: 0.4;
  }
}

@keyframes float-6 {
  0%, 100% {
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0.5;
  }
  30% {
    transform: translateY(-12px) translateX(-15px) scale(1.3);
    opacity: 0.8;
  }
  70% {
    transform: translateY(15px) translateX(8px) scale(0.7);
    opacity: 0.6;
  }
}

@keyframes spin-reverse {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}

@keyframes pulse-ring {
  0%, 100% {
    transform: scale(1);
    opacity: 0.4;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes pulse-ring-inner {
  0%, 100% {
    transform: scale(1);
    opacity: 0.2;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.6;
  }
}

@keyframes logo-morph {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    border-radius: 50%;
  }
  25% {
    transform: scale(1.05) rotate(2deg);
    border-radius: 45%;
  }
  50% {
    transform: scale(0.95) rotate(-1deg);
    border-radius: 55%;
  }
  75% {
    transform: scale(1.02) rotate(1deg);
    border-radius: 48%;
  }
}

@keyframes letter-bounce {
  0%, 100% {
    transform: translateY(0px) scale(1);
    color: #ffffff;
  }
  50% {
    transform: translateY(-8px) scale(1.1);
    color: #BA42FF;
    text-shadow: 0 0 15px rgba(186, 66, 255, 0.8);
  }
}

@keyframes logo-text-wave {
  0%, 100% {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
  }
  50% {
    text-shadow: 0 0 20px rgba(186, 66, 255, 0.6), 0 0 30px rgba(0, 225, 255, 0.4);
  }
}

@keyframes energy-wave-1 {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

@keyframes energy-wave-2 {
  0% {
    transform: scale(0.9);
    opacity: 0;
  }
  50% {
    opacity: 0.2;
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
  }
}

@keyframes energy-wave-3 {
  0% {
    transform: scale(1);
    opacity: 0;
  }
  50% {
    opacity: 0.1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

.animate-float-1 {
  animation: float-1 4s ease-in-out infinite;
}

.animate-float-2 {
  animation: float-2 3.5s ease-in-out infinite;
}

.animate-float-3 {
  animation: float-3 5s ease-in-out infinite;
}

.animate-float-4 {
  animation: float-4 4.5s ease-in-out infinite;
}

.animate-float-5 {
  animation: float-5 3s ease-in-out infinite;
}

.animate-float-6 {
  animation: float-6 6s ease-in-out infinite;
}

.animate-spin-reverse {
  animation: spin-reverse 8s linear infinite;
}

.animate-pulse-ring {
  animation: pulse-ring 3s ease-in-out infinite;
}

.animate-pulse-ring-inner {
  animation: pulse-ring-inner 2s ease-in-out infinite reverse;
}

.animate-logo-morph {
  animation: logo-morph 6s ease-in-out infinite;
}

.animate-letter-bounce {
  animation: letter-bounce 2s ease-in-out infinite;
}

.animate-logo-text-wave {
  animation: logo-text-wave 4s ease-in-out infinite;
}

.animate-energy-wave-1 {
  animation: energy-wave-1 3s ease-out infinite;
}

.animate-energy-wave-2 {
  animation: energy-wave-2 3s ease-out infinite 0.5s;
}

.animate-energy-wave-3 {
  animation: energy-wave-3 3s ease-out infinite 1s;
}

.animate-brand-pulse {
  animation: brand-pulse 1.5s ease-in-out infinite;
}

.animate-line-expand {
  animation: line-expand 1s ease-out 0.5s both, line-pulse 2s ease-in-out 1.5s infinite;
}

.animate-stage-text {
  animation: stage-text 0.5s ease-out, text-fade-pulse 2s ease-in-out 0.5s infinite;
}

.animate-progress-entrance {
  animation: progress-entrance 1s ease-out 0.8s both, progress-glow-continuous 3s ease-in-out 1.8s infinite;
}

.animate-shimmer-fast {
  animation: shimmer-fast 1.2s ease-in-out infinite;
}

.animate-progress-light-fast {
  animation: progress-light-fast 1.8s ease-in-out infinite;
}

.animate-percentage-update {
  animation: percentage-update 0.3s ease-out, percentage-pulse 2s ease-in-out 0.3s infinite;
}

.animate-particle-float-1 {
  animation: particle-float-1 4s ease-in-out infinite;
}

.animate-particle-float-2 {
  animation: particle-float-2 3.5s ease-in-out infinite;
}

.animate-particle-float-3 {
  animation: particle-float-3 4.5s ease-in-out infinite;
}

/* Custom border width utility */
.border-3 {
  border-width: 3px;
}
