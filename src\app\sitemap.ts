import { MetadataRoute } from 'next'

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://tregotech.com'
  const currentDate = new Date()
  
  // Define all pages with their priorities and change frequencies
  const pages = [
    {
      url: '',
      priority: 1.0,
      changeFrequency: 'weekly' as const,
    },
    {
      url: '/about',
      priority: 0.8,
      changeFrequency: 'monthly' as const,
    },
    {
      url: '/contact',
      priority: 0.7,
      changeFrequency: 'monthly' as const,
    },
    {
      url: '/policies',
      priority: 0.6,
      changeFrequency: 'yearly' as const,
    },
    {
      url: '/terms',
      priority: 0.6,
      changeFrequency: 'yearly' as const,
    },
  ]

  // Generate sitemap entries for both English and Arabic
  const sitemapEntries: MetadataRoute.Sitemap = []

  // Add entries for each language
  const locales = ['en', 'ar']
  
  locales.forEach(locale => {
    pages.forEach(page => {
      const url = locale === 'en' 
        ? `${baseUrl}${page.url}` 
        : `${baseUrl}/${locale}${page.url}`
      
      sitemapEntries.push({
        url,
        lastModified: currentDate,
        changeFrequency: page.changeFrequency,
        priority: page.priority,
      })
    })
  })

  return sitemapEntries
}
