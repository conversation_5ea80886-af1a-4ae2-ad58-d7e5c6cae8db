"use client";

import { useEffect, useRef } from "react";

interface PerformanceMetrics {
  fps: number;
  memoryUsage: number;
  renderTime: number;
}

export const usePerformanceMonitor = (componentName: string) => {
  const metricsRef = useRef<PerformanceMetrics>({ fps: 0, memoryUsage: 0, renderTime: 0 });
  const frameCountRef = useRef(0);
  const lastTimeRef = useRef(performance.now());
  const animationFrameRef = useRef<number | null>(null);

  useEffect(() => {
    const startTime = performance.now();

    const measureFPS = () => {
      const currentTime = performance.now();
      frameCountRef.current++;

      // Calculate FPS every second
      if (currentTime - lastTimeRef.current >= 1000) {
        metricsRef.current.fps = Math.round((frameCountRef.current * 1000) / (currentTime - lastTimeRef.current));
        frameCountRef.current = 0;
        lastTimeRef.current = currentTime;

        // Log performance metrics in development
        if (process.env.NODE_ENV === 'development') {
          console.log(`[${componentName}] Performance Metrics:`, {
            FPS: metricsRef.current.fps,
            'Memory (MB)': metricsRef.current.memoryUsage,
            'Render Time (ms)': metricsRef.current.renderTime
          });
        }
      }

      animationFrameRef.current = requestAnimationFrame(measureFPS);
    };

    // Measure render time
    const measureRenderTime = () => {
      const endTime = performance.now();
      metricsRef.current.renderTime = endTime - startTime;
    };

    // Measure memory usage (if available)
    const measureMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as { memory: { usedJSHeapSize: number } }).memory;
        metricsRef.current.memoryUsage = Math.round(memory.usedJSHeapSize / 1024 / 1024);
      }
    };

    // Start monitoring
    measureRenderTime();
    measureMemory();
    animationFrameRef.current = requestAnimationFrame(measureFPS);

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [componentName]);

  return metricsRef.current;
};
