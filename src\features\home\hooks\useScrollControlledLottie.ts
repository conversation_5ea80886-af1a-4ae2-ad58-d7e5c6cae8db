"use client";

import { useEffect, useRef } from "react";
import { ANIMATION_CONFIG, debugLog } from "@/config/animationConfig";

interface UseScrollControlledLottieProps {
  sectionId?: string;
  pauseOffset?: number; // How far to scroll past the section before pausing (in pixels)
  resumeOffset?: number; // How far back into the section before resuming (in pixels)
}

export const useScrollControlledLottie = ({
  sectionId = "landing",
  pauseOffset = 200, // Pause when 200px past the section
  resumeOffset = 100, // Resume when 100px back into the section
}: UseScrollControlledLottieProps = {}) => {
  const lottieInstanceRef = useRef<{ play: () => void; pause: () => void } | null>(null);
  const isPlayingRef = useRef(true);
  const sectionRef = useRef<HTMLElement | null>(null);

  // Function to register the lottie instance
  const registerLottieInstance = (instance: { play: () => void; pause: () => void }) => {
    lottieInstanceRef.current = instance;
    debugLog("Lottie instance registered for scroll control");
  };

  // Function to play the animation
  const playAnimation = () => {
    if (lottieInstanceRef.current && !isPlayingRef.current) {
      lottieInstanceRef.current.play();
      isPlayingRef.current = true;
      debugLog("🎬 Lottie animation resumed");
    }
  };

  // Function to pause the animation
  const pauseAnimation = () => {
    if (lottieInstanceRef.current && isPlayingRef.current) {
      lottieInstanceRef.current.pause();
      isPlayingRef.current = false;
      debugLog("⏸️ Lottie animation paused");
    }
  };

  useEffect(() => {
    // Find the section element
    const findSection = () => {
      const section = document.getElementById(sectionId) || document.querySelector(`[data-section="${sectionId}"]`);
      if (section) {
        sectionRef.current = section as HTMLElement;
        debugLog(`Section found: ${sectionId}`);
        return true;
      }
      return false;
    };

    // Wait for section to be available
    const waitForSection = () => {
      if (!findSection()) {
        setTimeout(waitForSection, 100);
        return;
      }
      initializeScrollControl();
    };

    const initializeScrollControl = () => {
      if (!sectionRef.current) return;

      let ticking = false;

      const handleScroll = () => {
        if (!ticking) {
          requestAnimationFrame(() => {
            checkScrollPosition();
            ticking = false;
          });
          ticking = true;
        }
      };

      const checkScrollPosition = () => {
        if (!sectionRef.current) return;

        const rect = sectionRef.current.getBoundingClientRect();
        const windowHeight = window.innerHeight;
        const scrollY = window.scrollY;

        // Calculate section boundaries
        const sectionTop = scrollY + rect.top;
        const sectionBottom = sectionTop + rect.height;
        const currentScrollPosition = scrollY + windowHeight;

        // Check if we've scrolled past the section by the pause offset
        const shouldPause = currentScrollPosition > sectionBottom + pauseOffset;
        
        // Check if we've scrolled back into the section by the resume offset
        const shouldResume = currentScrollPosition <= sectionBottom + resumeOffset;

        if (shouldPause && isPlayingRef.current) {
          pauseAnimation();
        } else if (shouldResume && !isPlayingRef.current) {
          playAnimation();
        }

        // Debug information (only in development)
        if (ANIMATION_CONFIG.DEBUG.ENABLED) {
          const debugInfo = {
            sectionTop,
            sectionBottom,
            currentScrollPosition,
            shouldPause,
            shouldResume,
            isPlaying: isPlayingRef.current,
            pauseThreshold: sectionBottom + pauseOffset,
            resumeThreshold: sectionBottom + resumeOffset,
          };
          
          // Only log when state changes to avoid spam
          if (shouldPause !== !isPlayingRef.current || shouldResume !== isPlayingRef.current) {
            debugLog("Scroll control debug:", debugInfo);
          }
        }
      };

      // Add scroll listener with passive option for better performance
      window.addEventListener('scroll', handleScroll, { passive: true });

      // Initial check
      checkScrollPosition();

      // Cleanup function
      return () => {
        window.removeEventListener('scroll', handleScroll);
      };
    };

    // Start the initialization process
    waitForSection();

    // Cleanup on unmount
    return () => {
      if (lottieInstanceRef.current) {
        lottieInstanceRef.current = null;
      }
    };
  }, [sectionId, pauseOffset, resumeOffset]);

  return {
    registerLottieInstance,
    playAnimation,
    pauseAnimation,
    isPlaying: () => isPlayingRef.current,
  };
};
