import "./globals.css";
import { ThemeProvider } from "@/lib/provider/theme-provider";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import LayoutWrapper from "./layoutWrapper";
import 'leaflet/dist/leaflet.css';
import Navbar from "@/components/shared/components/Navbar";
import Footer from "@/components/shared/components/Footer";
import StructuredData from "@/components/seo/StructuredData";
import WebVitalsOptimizer from "@/components/seo/WebVitalsOptimizer";
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'TregoTech - Leading Technology Solutions',
  description: 'TregoTech provides innovative mobile app development, web development, and AI automation solutions. Building the future of technology in Egypt and beyond.',
  keywords: 'mobile app development, web development, AI automation, technology solutions, Egypt, TregoTech',
  authors: [{ name: 'TregoTech' }],
  creator: 'TregoTech',
  publisher: 'TregoTech',
  icons: {
    icon: '/assets/FavIcon.ico',
    shortcut: '/assets/FavIcon.ico',
    apple: '/assets/FavIcon.ico',
  },
  openGraph: {
    title: 'TregoTech - Leading Technology Solutions',
    description: 'TregoTech provides innovative mobile app development, web development, and AI automation solutions.',
    url: 'https://tregotech.com',
    siteName: 'TregoTech',
    images: [
      {
        url: '/assets/BlueLogo.png',
        width: 1200,
        height: 630,
        alt: 'TregoTech Logo',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'TregoTech - Leading Technology Solutions',
    description: 'TregoTech provides innovative mobile app development, web development, and AI automation solutions.',
    images: ['/assets/BlueLogo.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: 'https://tregotech.com',
    languages: {
      'en': 'https://tregotech.com',
      'ar': 'https://tregotech.com/ar',
    },
  },
  verification: {
    google: process.env.NEXT_PUBLIC_GOOGLE_VERIFICATION,
    yandex: process.env.NEXT_PUBLIC_YANDEX_VERIFICATION,
    yahoo: process.env.NEXT_PUBLIC_YAHOO_VERIFICATION,
    other: {
      'msvalidate.01': process.env.NEXT_PUBLIC_BING_VERIFICATION || '',
    },
  },
};

export default async function LocaleLayout({
  children,
}: {
  children: React.ReactNode;
}) {


  return (
    <html>
      <head>
        {/* Favicon */}
        <link rel="icon" href="/assets/FavIcon.ico" sizes="any" />
        <link rel="icon" href="/assets/FavIcon.ico" type="image/x-icon" />
        <link rel="shortcut icon" href="/assets/FavIcon.ico" />
        <link rel="apple-touch-icon" href="/assets/FavIcon.ico" />

        {/* PWA Manifest */}
        <link rel="manifest" href="/manifest.json" />

        {/* CRITICAL: Performance optimizations for LCP */}
        <link rel="preconnect" href="https://prod.spline.design" />
        <link rel="dns-prefetch" href="https://prod.spline.design" />

        {/* CRITICAL: Preload essential fonts */}
        <link
          rel="preload"
          href="/fonts/Orbitron-Regular.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/fonts/Sora-Regular.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />

        {/* CRITICAL: Inline critical CSS for above-the-fold content */}
        <style dangerouslySetInnerHTML={{
          __html: `
            /* Critical CSS for LCP */
            body {
              margin: 0;
              background: transparent;
              color: #000000;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
            .min-h-screen { min-height: 100vh; }
            .relative { position: relative; }
            .absolute { position: absolute; }
            .fixed { position: fixed; }
            .inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
            .z-10 { z-index: 10; }
            .z-50 { z-index: 50; }
            .flex { display: flex; }
            .items-center { align-items: center; }
            .justify-center { justify-content: center; }
            .text-center { text-align: center; }
            .text-white { color: white; }
            .bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)); }
            .from-\\[\\#0a0a0a\\] { --tw-gradient-from: #0a0a0a; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(10, 10, 10, 0)); }
            .via-\\[\\#1a1a2e\\] { --tw-gradient-stops: var(--tw-gradient-from), #1a1a2e, var(--tw-gradient-to, rgba(26, 26, 46, 0)); }
            .to-\\[\\#16213e\\] { --tw-gradient-to: #16213e; }
          `
        }} />
        {/* Structured Data */}
        <StructuredData type="organization" />
        <StructuredData type="website" />
      </head>
      <body>
        <WebVitalsOptimizer />
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <LayoutWrapper>
            <Navbar />
           {children}
            <Footer />
          </LayoutWrapper>
          <ToastContainer position="top-right" autoClose={3000} />
        </ThemeProvider>
      </body>
    </html>
  );
}
