import { AuthOptions } from "next-auth";
import <PERSON>A<PERSON> from "next-auth";
import { JWT } from "next-auth/jwt";
import apiClient from "@/lib/api-client/apiClient";
import CredentialsProvider from "next-auth/providers/credentials";
import GoogleProvider from "next-auth/providers/google";
import { AuthResponse, ProfileResponse } from "@/types/auth";

interface CustomUser {
  id: string;
  email: string;
  name: string;
  accessToken: string;
  refreshToken: string;
  accessTokenExpiresAt: string;
  refreshTokenExpiresAt: string;
}



export const authConfig: AuthOptions = {
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "text" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        console.log("Authorize Function - Received Credentials:", credentials);
        try {
          const response = await apiClient.post<AuthResponse>(
            "/auth/api/signin",
            {
              email: credentials?.email,
              password: credentials?.password,
            }
          );
          console.log("Authorize Function - API Response:", response);

          const user = {
            accessToken: response.accessToken,
            refreshToken: response.refreshToken,
            accessTokenExpiresAt: response.access_token_remTime,
            refreshTokenExpiresAt: response.refresh_token_remTime,
            email: credentials?.email,
            name: response.name || "User",
            id: response.userId || "1",
          };

          if (
            user &&
            user.accessToken &&
            user.refreshToken &&
            user.accessTokenExpiresAt &&
            user.refreshTokenExpiresAt
          ) {
            console.log("Authorize Function - User Object:", user);
            return {
              id: user.id,
              name: user.name,
              email: credentials?.email,
              accessToken: user.accessToken,
              refreshToken: user.refreshToken,
              accessTokenExpiresAt: user.accessTokenExpiresAt,
              refreshTokenExpiresAt: user.refreshTokenExpiresAt,
            };
          }

          console.log("Authorize Function - User Not Valid");
          return null;
        } catch (error) {
          console.error("Login error:", error);
          return null;
        }
      },
    }),

    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code",
        },
      },
    }),
  ],

  callbacks: {
    async jwt({ token, account, user }): Promise<JWT> {

      if (account || user) {
        if (account?.provider === "google") {
          try {
            const response = await apiClient.post<AuthResponse>(
              "/auth/google/callback",
              {
                access_token: account.access_token,
                id_token: account.id_token,
              }
            );
            console.log("access_token:", account.access_token);
            console.log("id_token:", account.id_token);
            console.log("Google callback response:", response);

            token.accessToken = response.accessToken || account.access_token;
            token.refreshToken = response.refreshToken || account.refresh_token;
            token.id = response.userId || token.sub;
            token.accessTokenExpiresAt =
              Date.now() + response.access_token_remTime * 1000;
            token.refreshTokenExpiresAt =
              Date.now() + response.refresh_token_remTime * 1000;

            console.log("Updated token (Google):", token);
          } catch (error) {
            console.error("Google validation failed:", error);
            token.error = "GoogleValidationFailed";
          }
        } else if (account?.provider === "credentials") {
          const customUser = user as CustomUser;
          token.accessToken = customUser.accessToken;
          token.refreshToken = customUser.refreshToken;
          token.id = customUser.id;
          token.accessTokenExpiresAt = customUser.accessTokenExpiresAt;
          token.refreshTokenExpiresAt = customUser.refreshTokenExpiresAt;
          token.email = customUser.email;
          token.name = customUser.name;

          console.log("Updated token (Credentials):", token);
        }
      }

      console.log("Final token returned from jwt callback:", token);
      return token;
    },

    async session({ session, token }) {
      console.log("Token received in session callback:", token);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const customSession = session as any;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const customToken = token as any;
      if (customToken) {
        customSession.user.id = customToken.id;
        customSession.user.sub = customToken.sub || customToken.id;
        customSession.user.email = customToken.email;
        customSession.user.name = customToken.name;
        customSession.user.accessToken = customToken.accessToken || "";
        customSession.user.refreshToken = customToken.refreshToken || "";
        customSession.user.accessTokenExpiresAt = customToken.accessTokenExpiresAt || "";
        customSession.user.refreshTokenExpiresAt = customToken.refreshTokenExpiresAt || "";

        try {
          const response = await apiClient.get<ProfileResponse>(
            "/auth/profile",
            {
              headers: {
                Authorization: `Bearer ${customToken.accessToken}`,
              },
            }
          );
          console.log("User data API response:", response);

          customSession.user.name =
            response.user?.user?.firstname && response.user?.user?.lastname
              ? `${response.user.user.firstname} ${response.user.user.lastname}`
              : response.user?.user?.firstname || customSession.user.name || "User";
          customSession.user.email = response.user?.user?.email || customSession.user.email;
          customSession.user.phonenumber =
            response.user?.user?.phonenumber || customSession.user.phonenumber;
          customSession.user.country =
            response.user?.user?.country || customSession.user.country;
        } catch (error) {
          console.error("Error fetching user data:", error);
        }
      }

      console.log("Updated session in session callback:", session);
      return session;
    },
  },
  secret: process.env.Next_Auth_Secret,
  pages: {
    signIn: "/login",
  },
};

const handler = NextAuth(authConfig);
export { handler as GET, handler as POST };
