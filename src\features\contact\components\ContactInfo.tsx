interface ContactInfoProps {
  infoRef: React.RefObject<HTMLDivElement | null>;
}

export default function ContactInfo({ infoRef }: ContactInfoProps) {
  return (
    <div ref={infoRef} className="space-y-8">
      {/* Contact Information */}
      <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-xl rounded-3xl p-8 border border-gray-700/30 shadow-2xl">
        <h3 className="text-2xl font-bold text-white mb-6 font-orbitron">
          Get in Touch
        </h3>
        
        <div className="space-y-6">
          <div>
            <h4 className="text-lg font-bold text-white mb-2 font-orbitron">Email</h4>
            <p className="text-gray-300 font-sora"><EMAIL></p>
          </div>
          
          <div>
            
            <h4 className="text-lg font-bold text-white mb-2 font-orbitron">Phone</h4>
            <p className="text-gray-300 font-sora">+201112628619</p>
          </div>
          
          <div>
            <h4 className="text-lg font-bold text-white mb-2 font-orbitron">Response Time</h4>
            <p className="text-gray-300 font-sora">We typically respond within 24 hours</p>
          </div>
        </div>
      </div>

      {/* Why Choose Us */}
      <div className="bg-gradient-to-br from-[#302cff]/10 to-purple-500/10 backdrop-blur-xl rounded-3xl p-8 border border-[#302cff]/30 shadow-2xl">
        <h3 className="text-2xl font-bold text-white mb-6 font-orbitron">
          Why Choose TregoTech?
        </h3>
        
        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <div className="w-2 h-2 bg-purple-400 rounded-full mt-3 flex-shrink-0"></div>
            <p className="text-gray-300 font-sora">Expert team with years of experience</p>
          </div>
          
          <div className="flex items-start space-x-3">
            <div className="w-2 h-2 bg-purple-400 rounded-full mt-3 flex-shrink-0"></div>
            <p className="text-gray-300 font-sora">Cutting-edge technology solutions</p>
          </div>
          
          <div className="flex items-start space-x-3">
            <div className="w-2 h-2 bg-purple-400 rounded-full mt-3 flex-shrink-0"></div>
            <p className="text-gray-300 font-sora">Dedicated support and maintenance</p>
          </div>
          
          <div className="flex items-start space-x-3">
            <div className="w-2 h-2 bg-purple-400 rounded-full mt-3 flex-shrink-0"></div>
            <p className="text-gray-300 font-sora">Transparent communication throughout</p>
          </div>
        </div>
      </div>
    </div>
  );
}
