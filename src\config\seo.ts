export const SEO_CONFIG = {
  // Base configuration
  baseUrl: 'https://tregotech.com',
  siteName: 'TregoTech',
  defaultTitle: 'TregoTech - Leading Technology Solutions',
  titleTemplate: '%s | TregoTech',
  
  // Default metadata
  defaultDescription: 'TregoTech provides innovative mobile app development, web development, and AI automation solutions. Building the future of technology in Egypt and beyond.',
  defaultKeywords: 'mobile app development, web development, AI automation, technology solutions, Egypt, TregoTech',
  
  // Social media
  social: {
    twitter: '@tregotech',
    facebook: 'tregotech',
    linkedin: 'company/tregotech',
  },
  
  // Contact information
  contact: {
    email: '<EMAIL>',
    phone: '+201112628619',
    address: 'Egypt',
  },
  
  // Organization schema
  organization: {
    name: 'TregoTech',
    alternateName: 'Trego Technology',
    description: 'Leading technology solutions provider in Egypt specializing in mobile app development, web development, and AI automation.',
    foundingDate: '2023',
    logo: '/assets/BlueLogo.png',
    services: [
      'Mobile App Development',
      'Web Development',
      'AI Automation',
      'Cloud Solutions',
      'System Design',
      'DevOps & Deployment'
    ],
    sameAs: [
      'https://apps.apple.com/eg/app/trego/id6742484811',
      'https://play.google.com/store/apps/details?id=com.TregoTech.TregoApp'
    ]
  },
  
  // Page-specific SEO configurations
  pages: {
    home: {
      title: 'TregoTech - Leading Technology Solutions',
      description: 'TregoTech provides innovative mobile app development, web development, and AI automation solutions. Building the future of technology in Egypt and beyond.',
      keywords: 'mobile app development, web development, AI automation, technology solutions, Egypt, TregoTech, Trego app',
      path: '/',
    },
    about: {
      title: 'About Us - TregoTech | Leading Technology Solutions in Egypt',
      description: 'Learn about TregoTech\'s mission to revolutionize travel technology in Egypt. Discover our vision, values, and commitment to innovative mobile app development, web solutions, and AI automation.',
      keywords: 'about TregoTech, travel technology Egypt, mobile app development company, AI automation, web development, technology solutions, Egyptian tech company, innovation, mission, vision',
      path: '/about',
    },
    contact: {
      title: 'Contact Us - TregoTech | Get in Touch for Technology Solutions',
      description: 'Contact TregoTech for innovative mobile app development, web development, and AI automation solutions. Reach out to our expert team in Egypt. Email: <EMAIL> | Phone: +201112628619',
      keywords: 'contact TregoTech, mobile app development contact, web development Egypt, AI automation services, technology consultation, TregoTech phone, TregoTech email, Egyptian tech company contact',
      path: '/contact',
    },
    policies: {
      title: 'Privacy Policy - TregoTech | Data Protection & Privacy Practices',
      description: 'Read TregoTech\'s comprehensive privacy policy. Learn how we protect your data, handle personal information, and maintain privacy standards for our mobile app and web services.',
      keywords: 'TregoTech privacy policy, data protection, privacy practices, personal information, mobile app privacy, web service privacy, GDPR compliance, data security',
      path: '/policies',
    },
    terms: {
      title: 'Terms of Service - TregoTech | Service Terms & Conditions',
      description: 'Read TregoTech\'s terms of service and conditions. Understand the terms governing the use of our mobile app development, web development, and AI automation services.',
      keywords: 'TregoTech terms of service, terms and conditions, service agreement, mobile app terms, web development terms, AI automation terms, user agreement, legal terms',
      path: '/terms',
    },
  },
  
  // Robots configuration
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  
  // Open Graph defaults
  openGraph: {
    type: 'website',
    locale: 'en_US',
    siteName: 'TregoTech',
    images: [
      {
        url: '/assets/BlueLogo.png',
        width: 1200,
        height: 630,
        alt: 'TregoTech Logo',
      },
    ],
  },
  
  // Twitter defaults
  twitter: {
    card: 'summary_large_image',
    site: '@tregotech',
    creator: '@tregotech',
  },
  
  // Structured data types
  structuredDataTypes: {
    organization: 'Organization',
    website: 'WebSite',
    breadcrumb: 'BreadcrumbList',
    contactPage: 'ContactPage',
    aboutPage: 'AboutPage',
    service: 'Service',
    mobileApplication: 'MobileApplication',
  },
  
  // Language configuration
  languages: {
    default: 'en',
    supported: ['en', 'ar'],
    alternates: {
      'en': '',
      'ar': '/ar',
    },
  },
  
  // Performance configuration
  performance: {
    enableWebVitalsOptimization: true,
    enableImageOptimization: true,
    enableFontOptimization: true,
    enableResourceHints: true,
  },
  
  // Analytics and tracking
  analytics: {
    googleAnalytics: process.env.NEXT_PUBLIC_GA_ID,
    googleTagManager: process.env.NEXT_PUBLIC_GTM_ID,
    facebookPixel: process.env.NEXT_PUBLIC_FB_PIXEL_ID,
  },
};

// Helper functions
export const getSEOConfig = (page: keyof typeof SEO_CONFIG.pages) => {
  const pageConfig = SEO_CONFIG.pages[page];
  const baseConfig = SEO_CONFIG;
  
  return {
    title: pageConfig.title,
    description: pageConfig.description,
    keywords: pageConfig.keywords,
    canonical: `${baseConfig.baseUrl}${pageConfig.path}`,
    openGraph: {
      ...baseConfig.openGraph,
      title: pageConfig.title,
      description: pageConfig.description,
      url: `${baseConfig.baseUrl}${pageConfig.path}`,
    },
    twitter: {
      ...baseConfig.twitter,
      title: pageConfig.title,
      description: pageConfig.description,
    },
    alternates: {
      canonical: `${baseConfig.baseUrl}${pageConfig.path}`,
      languages: Object.entries(baseConfig.languages.alternates).reduce(
        (acc, [lang, prefix]) => ({
          ...acc,
          [lang]: `${baseConfig.baseUrl}${prefix}${pageConfig.path}`,
        }),
        {}
      ),
    },
  };
};

export const generateBreadcrumbs = (path: string) => {
  const segments = path.split('/').filter(Boolean);
  const breadcrumbs = [{ name: 'Home', url: '/' }];
  
  let currentPath = '';
  segments.forEach(segment => {
    currentPath += `/${segment}`;
    const name = segment.charAt(0).toUpperCase() + segment.slice(1).replace('-', ' ');
    breadcrumbs.push({ name, url: currentPath });
  });
  
  return breadcrumbs;
};
