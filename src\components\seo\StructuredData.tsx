'use client';

interface BreadcrumbItem {
  name: string;
  url: string;
}

interface StructuredDataProps {
  type: 'organization' | 'website' | 'breadcrumb' | 'contactPage' | 'aboutPage';
  data?: {
    breadcrumbs?: BreadcrumbItem[];
    [key: string]: unknown;
  };
}

export default function StructuredData({ type, data }: StructuredDataProps) {
  const getStructuredData = () => {
    const baseUrl = 'https://tregotech.com';
    
    switch (type) {
      case 'organization':
        return {
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": "TregoTech",
          "alternateName": "Trego Technology",
          "url": baseUrl,
          "logo": `${baseUrl}/assets/BlueLogo.png`,
          "description": "TregoTech provides innovative mobile app development, web development, and AI automation solutions. Building the future of technology in Egypt and beyond.",
          "foundingDate": "2023",
          "founders": [
            {
              "@type": "Person",
              "name": "TregoTech Team"
            }
          ],
          "address": {
            "@type": "PostalAddress",
            "addressCountry": "EG",
            "addressRegion": "Egypt"
          },
          "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "+201112628619",
            "email": "<EMAIL>",
            "contactType": "customer service",
            "availableLanguage": ["English", "Arabic"]
          },
          "sameAs": [
            "https://apps.apple.com/eg/app/trego/id6742484811",
            "https://play.google.com/store/apps/details?id=com.TregoTech.TregoApp"
          ],
          "services": [
            "Mobile App Development",
            "Web Development", 
            "AI Automation",
            "Cloud Solutions",
            "System Design",
            "DevOps & Deployment"
          ]
        };

      case 'website':
        return {
          "@context": "https://schema.org",
          "@type": "WebSite",
          "name": "TregoTech",
          "url": baseUrl,
          "description": "Leading technology solutions provider in Egypt specializing in mobile app development, web development, and AI automation.",
          "publisher": {
            "@type": "Organization",
            "name": "TregoTech"
          },
          "potentialAction": {
            "@type": "SearchAction",
            "target": {
              "@type": "EntryPoint",
              "urlTemplate": `${baseUrl}/search?q={search_term_string}`
            },
            "query-input": "required name=search_term_string"
          },
          "inLanguage": ["en", "ar"]
        };

      case 'breadcrumb':
        return {
          "@context": "https://schema.org",
          "@type": "BreadcrumbList",
          "itemListElement": data?.breadcrumbs?.map((item, index) => ({
            "@type": "ListItem",
            "position": index + 1,
            "name": item.name,
            "item": `${baseUrl}${item.url}`
          })) || []
        };

      case 'contactPage':
        return {
          "@context": "https://schema.org",
          "@type": "ContactPage",
          "name": "Contact TregoTech",
          "description": "Get in touch with TregoTech for innovative technology solutions",
          "url": `${baseUrl}/contact`,
          "mainEntity": {
            "@type": "Organization",
            "name": "TregoTech",
            "contactPoint": {
              "@type": "ContactPoint",
              "telephone": "+201112628619",
              "email": "<EMAIL>",
              "contactType": "customer service"
            }
          }
        };

      case 'aboutPage':
        return {
          "@context": "https://schema.org",
          "@type": "AboutPage",
          "name": "About TregoTech",
          "description": "Learn about TregoTech's mission to revolutionize travel technology in Egypt",
          "url": `${baseUrl}/about`,
          "mainEntity": {
            "@type": "Organization",
            "name": "TregoTech",
            "description": "Pioneering the future of travel technology in Egypt and beyond",
            "foundingDate": "2023",
            "mission": "To revolutionize travel technology and make transportation more accessible and efficient across Egypt"
          }
        };

      default:
        return null;
    }
  };

  const structuredData = getStructuredData();

  if (!structuredData) return null;

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData, null, 2)
      }}
    />
  );
}
