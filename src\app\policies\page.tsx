import PoliciesMain from "@/features/policies/components/PoliciesMain";
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Privacy Policy - TregoTech | Data Protection & Privacy Practices',
  description: 'Read TregoTech\'s comprehensive privacy policy. Learn how we protect your data, handle personal information, and maintain privacy standards for our mobile app and web services.',
  keywords: 'TregoTech privacy policy, data protection, privacy practices, personal information, mobile app privacy, web service privacy, GDPR compliance, data security',
  authors: [{ name: 'TregoTech' }],
  creator: 'TregoTech',
  publisher: 'TregoTech',
  openGraph: {
    title: 'TregoTech Privacy Policy - Your Data Protection Matters',
    description: 'Understand how TregoTech protects your privacy and handles personal data. Comprehensive privacy policy for our technology services.',
    url: 'https://tregotech.com/policies',
    siteName: 'TregoTech',
    images: [
      {
        url: '/assets/BlueLogo.png',
        width: 1200,
        height: 630,
        alt: 'TregoTech Privacy Policy',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'TregoTech Privacy Policy - Your Data Protection Matters',
    description: 'Understand how TregoTech protects your privacy and handles personal data.',
    images: ['/assets/BlueLogo.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: 'https://tregotech.com/policies',
    languages: {
      'en': 'https://tregotech.com/policies',
      'ar': 'https://tregotech.com/ar/policies',
    },
  },
};

export default function PoliciesPage() {
  return (
    <>
      <PoliciesMain />
    </>
  );
}
