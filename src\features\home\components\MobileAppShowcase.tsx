"use client";

import Image from "next/image";
import { useMobileAppShowcase } from "../hooks/useMobileAppShowcase";
import {
  APP_STORE_LINKS,
  APP_SCREENSHOTS,
  APP_FEATURES,
  TRAVEL_OPTIONS,
  APP_DESCRIPTION,
  SECTION_TITLES
} from "../consts/mobileAppShowcaseConsts";

export default function MobileAppShowcase() {
  const {
    refs: { sectionRef, titleRef, appLogoRef, storeLinksRef, descriptionRef, screenshotsRef, featuresRef, travelOptionsRef },
    expandedOption,
    isTransitioning
  } = useMobileAppShowcase();


  // Removed background transition scroll handler - now handled by SmoothBackgroundTransition component
  return (
    <section
      ref={sectionRef}
      id="mobile-app-showcase"
      className="relative min-h-screen pt-32 pb-16 px-8 lg:px-16 z-10"
    >
      {/* Remove the white overlay that conflicts with our background transition system */}

      <div className="relative max-w-7xl mx-auto">
        {/* Section Header */}
        <div ref={titleRef} className="text-center mb-16">
          <h2
            className="app-title text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-8 font-orbitron leading-tight"
          >
            Meet <span className="text-purple-400">Trego App</span>
          </h2>

          {/* App Logo */}
          <div
            ref={appLogoRef}
            className="app-logo mb-8"
          >
            <Image
              src="/assets/trego_app/logo.png"
              alt="Trego App Logo"
              width={120}
              height={120}
              priority
              className="mx-auto rounded-2xl"
            />
          </div>

          {/* Store Icons - Small hover effects */}
          <div
            ref={storeLinksRef}
            className="app-store-links flex flex-col md:flex-row justify-center items-center space-y-6 md:space-y-0 md:space-x-8 mb-8"
          >
            <a
              href={APP_STORE_LINKS.apple}
              target="_blank"
              rel="noopener noreferrer"
              className="cursor-hover-effect"
              data-cursor="pointer"
            >
              <Image
                src="/assets/trego_app/appStore.webp"
                alt="Download on App Store"
                width={160}
                height={48}
                priority
                className=""
              />
            </a>
            <a
              href={APP_STORE_LINKS.google}
              target="_blank"
              rel="noopener noreferrer"
              className="cursor-hover-effect"
              data-cursor="pointer"
            >
              <Image
                src="/assets/trego_app/googlePlay.webp"
                alt="Get it on Google Play"
                width={160}
                height={48}
                priority
                className=""
              />
            </a>
            <div className="flex items-center space-x-3 text-purple-400 font-bold text-lg" data-cursor="pointer">
              <span className="text-3xl">🌐</span>
              <span>Website</span>
            </div>
          </div>

          <div ref={descriptionRef} className="app-description">
            <p
              className="text-lg md:text-xl text-gray-300 max-w-4xl mx-auto font-sora leading-relaxed"
            >
              {APP_DESCRIPTION.main}
              <span className="block mt-2 text-purple-300 font-semibold">
                {APP_DESCRIPTION.subtitle}
              </span>
            </p>
          </div>
        </div>

        {/* App Screenshots and Features Layout */}
        <div
          ref={screenshotsRef}
          className="mobile-showcase-screenshots grid grid-cols-1 lg:grid-cols-2 gap-28 items-center max-w-6xl mx-auto"
          style={{
            minHeight: '600px'
          }}
        >
          {/* LEFT SIDE - App Screenshots - MUCH BIGGER */}
          <div className="flex flex-col md:flex-row justify-center items-center space-y-12 md:space-y-0 md:space-x-12">

            {/* Main App Screenshot - HUGE */}
            <div
              className="app-screenshot-main mobile-showcase-screenshot mobile-screen-main w-[200px] md:w-[280px] aspect-[9/19] rounded-[2rem] overflow-hidden border-4 border-gray-900 shadow-2xl"
              id="main-screenshot"
            >
                <Image
                  src="/assets/trego_app/app.webp"
                  alt="Trego App Main Interface"
                  width={280}
                  height={560}
                  priority
                  className="object-cover w-full h-full"
                />
            </div>

            {/* Additional Screenshots Grid - MUCH BIGGER */}
            <div className="grid grid-cols-2 md:gap-24 gap-4">
              {APP_SCREENSHOTS.map((screenshot, index) => (
                <div
                  key={index}
                  className="app-screenshot-additional mobile-showcase-screenshot mobile-screen-additional w-[170px] aspect-[9/19] rounded-[1.3rem] overflow-hidden border-3 border-gray-700 shadow-2xl relative"
                >
                    <Image
                      src={screenshot.src}
                      alt={screenshot.alt}
                      width={170}
                      height={340}
                      priority
                      className="object-cover w-full h-full"
                    />
                </div>
              ))}
            </div>
          </div>

          {/* RIGHT SIDE - App Features */}
          <div
            ref={featuresRef}
            className="w-full"
          >
            {/* Section Header */}
            <div className="text-center mb-12">
              <h3 className="why-choose-title mobile-showcase-title text-3xl md:text-4xl font-bold text-white mb-4 font-orbitron">
                {SECTION_TITLES.whyChoose}
              </h3>
              <p className="why-choose-description mobile-showcase-description text-gray-300 font-sora text-lg max-w-2xl mx-auto">
                {SECTION_TITLES.whyChooseDescription}
              </p>
            </div>

            {/* Creative Feature Cards Grid - 2x3 Layout */}
            <div className="why-choose-cards-grid grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto">
              {APP_FEATURES.map((feature, index) => (
                <div
                  key={index}
                  className="app-feature-card why-choose-card mobile-showcase-card relative overflow-hidden rounded-xl shadow-md h-48"
                >
                  {/* Main Card Background - Enhanced for Dark/Purple Theme */}
                  <div className={`${feature.bgColor} backdrop-blur-sm border border-[#302cff]/30 p-4 h-full flex flex-col items-center justify-center text-center hover:border-[#302cff]/60 transition-all duration-300`}>
                    {/* Icon - Centered */}
                    <div className={`flex items-center justify-center w-12 h-12 ${feature.iconBg} backdrop-blur-sm rounded-xl mb-3 group-hover:scale-110 transition-transform duration-300`}>
                      <span className="text-2xl">{feature.icon}</span>
                    </div>

                    {/* Content - Centered */}
                    <div className="flex flex-col items-center justify-center">
                      <h4 className={`text-lg font-bold ${feature.textColor} mb-2 font-orbitron text-center`}>
                        {feature.title}
                      </h4>
                      <p className={`${feature.textColor} opacity-80 font-sora text-xs leading-relaxed text-center max-w-[200px]`}>
                        {feature.description}
                      </p>
                    </div>

                    {/* Subtle Decorative Elements - Dark Blue Theme */}
                    <div className="absolute top-0 right-0 w-8 h-8 bg-[#302cff]/30 rounded-full -translate-y-4 translate-x-4"></div>
                    <div className="absolute bottom-0 left-0 w-6 h-6 bg-[#302cff]/30 rounded-full translate-y-3 -translate-x-3"></div>
                  </div>

                  {/* Hover Overlay - Dark Blue Theme */}
                  <div className="absolute inset-0 bg-[#302cff]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Travel Options Section */}
        <div
          ref={travelOptionsRef}
          className="mt-32 text-center"
        >
          <div className="mb-16">
            <h3 className="travel-way-title mobile-showcase-title text-4xl md:text-5xl font-bold text-white mb-6 font-orbitron">
              {SECTION_TITLES.travelWay}
            </h3>
            <p className="travel-way-description mobile-showcase-description text-xl text-gray-300 max-w-3xl mx-auto font-sora leading-relaxed">
              {SECTION_TITLES.travelWayDescription}
            </p>
          </div>

          {/* Travel Options Grid */}
          <div className="travel-way-cards-grid grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            {TRAVEL_OPTIONS.map((option, index) => (
              <div key={index} className="travel-option-container">
                {/* Main Travel Option Card */}
                <div
                  className="travel-option-card travel-way-card mobile-showcase-card relative overflow-hidden rounded-2xl bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-xl border p-8"
                >
                  {/* Content */}
                  <div className="relative z-10 text-center">
                    {/* Icon */}
                    <div className={`inline-flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br ${option.color} mb-6 text-4xl`}>
                      {option.icon}
                    </div>

                    {/* Title */}
                    <h4 className="text-2xl font-bold text-white mb-4 font-orbitron">
                      {option.title}
                    </h4>

                    {/* Description */}
                    <p className="text-gray-300 font-sora leading-relaxed">
                      {option.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Expanded Travel Option Details - HIDDEN SINCE CARDS ARE NOT CLICKABLE */}
          <div className="hidden">
            <div className={`travel-option-details bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur-xl border border-gray-700/50 rounded-3xl p-8 md:p-12 max-w-6xl mx-auto shadow-2xl transition-all duration-[1200ms] ease-out transform ${
              expandedOption !== null
                ? 'scale-100 opacity-100 translate-y-0 rotate-0'
                : 'scale-90 opacity-0 translate-y-12 rotate-1'
            }`}>
              {expandedOption !== null && (
                <>
                  {/* Main Content Layout */}
                  <div className="space-y-12">

                    {/* Top Section - Image and Stats Side by Side */}
                    <div className="grid grid-cols-1 xl:grid-cols-2 gap-12">

                      {/* Left - Large Feature Image */}
                      <div className="relative">
                        <div className="aspect-[16/9] rounded-2xl overflow-hidden shadow-2xl relative">
                          {/* Actual Travel Image with Smooth Transitions */}
                          <div className={`absolute inset-0 transition-all duration-1000 ease-in-out ${
                            isTransitioning ? 'opacity-0 scale-110' : 'opacity-100 scale-100'
                          }`}>
                            <Image
                              src={TRAVEL_OPTIONS[expandedOption].detailedInfo.image}
                              alt={`${TRAVEL_OPTIONS[expandedOption].title} - Travel Experience`}
                              fill
                              className="w-full h-full object-cover transition-transform duration-1000 ease-out hover:scale-105"
                            />
                            {/* Overlay with gradient */}
                            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"></div>
                          </div>

                          {/* Floating Stats Cards with Smooth Animations */}
                          <div className={`absolute bottom-6 left-6 right-6 transition-all duration-1200 ease-out ${
                            isTransitioning ? 'opacity-0 translate-y-8' : 'opacity-100 translate-y-0'
                          }`}>
                            <div className="grid grid-cols-3 gap-4">
                              {Object.entries(TRAVEL_OPTIONS[expandedOption].detailedInfo.stats).map(([key, value], index) => (
                                <div
                                  key={key}
                                  className={`bg-white/15 backdrop-blur-md rounded-xl p-4 text-center border border-white/30 hover:bg-white/20 transition-all duration-300 hover:scale-105 ${
                                    isTransitioning ? 'opacity-0 translate-y-4' : 'opacity-100 translate-y-0'
                                  }`}
                                  style={{
                                    transitionDelay: isTransitioning ? '0ms' : `${index * 100}ms`
                                  }}
                                >
                                  <div className="text-2xl md:text-3xl font-bold text-white font-orbitron">{value}</div>
                                  <div className="text-sm text-gray-200 capitalize font-sora">{key}</div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Right - Title and Stats with Smooth Animations */}
                      <div className="space-y-8">
                        {/* Title Section with Smooth Transitions */}
                        <div className={`text-center xl:text-left transition-all duration-1000 ease-out ${
                          isTransitioning ? 'opacity-0 translate-x-8' : 'opacity-100 translate-x-0'
                        }`}>
                          <h4 className="text-3xl md:text-4xl font-bold text-white mb-4 font-orbitron transition-colors duration-500 hover:text-purple-300">
                            {TRAVEL_OPTIONS[expandedOption].title}
                          </h4>
                          <p className="text-lg text-gray-300 font-sora leading-relaxed transition-colors duration-300 hover:text-gray-200">
                            {TRAVEL_OPTIONS[expandedOption].detailedInfo.benefits}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Bottom Section - Full Width Features Grid with Smooth Animations */}
                    <div className={`w-full transition-all duration-1000 ease-out ${
                      isTransitioning ? 'opacity-0 translate-y-12' : 'opacity-100 translate-y-0'
                    }`}>
                      <h5 className={`text-2xl font-bold text-white mb-6 font-orbitron text-center transition-all duration-800 ease-out hover:text-purple-300 ${
                        isTransitioning ? 'opacity-0 -translate-y-4' : 'opacity-100 translate-y-0'
                      }`}>
                        Key Features & Benefits
                      </h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {TRAVEL_OPTIONS[expandedOption].detailedInfo.features.map((feature, featureIndex) => (
                          <div
                            key={featureIndex}
                            className={`bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50 hover:border-purple-500/50 transition-all duration-500 hover:scale-[1.02] hover:bg-gray-800/70 hover:shadow-lg hover:shadow-purple-500/20 ${
                              isTransitioning ? 'opacity-0 translate-y-8 scale-95' : 'opacity-100 translate-y-0 scale-100'
                            }`}
                            style={{
                              transitionDelay: isTransitioning ? '0ms' : `${featureIndex * 100}ms`
                            }}
                          >
                            <div className="flex items-start space-x-4">
                              <div className={`flex-shrink-0 w-8 h-8 rounded-lg bg-gradient-to-br ${TRAVEL_OPTIONS[expandedOption].color} flex items-center justify-center text-white font-bold text-sm transition-all duration-300 hover:scale-110 hover:rotate-12`}>
                                ✓
                              </div>
                              <div>
                                <span className="text-white font-sora font-medium leading-relaxed transition-colors duration-300 hover:text-gray-100">{feature}</span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Call to Action */}
          <div className="mt-16">
            <p className="text-lg text-purple-300 font-semibold font-sora mb-4">
              {SECTION_TITLES.callToAction}
            </p>
            <div className="flex justify-center items-center space-x-4">
              <span className="text-gray-400 font-sora">{SECTION_TITLES.callToActionSubtext}</span>
              <span className="text-2xl">🎯</span>
            </div>
          </div>
        </div>

      </div>
    </section>
  );
}