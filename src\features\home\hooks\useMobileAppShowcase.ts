"use client";

import { useRef, useEffect, useState } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { ANIMATION_CONFIG, debugLog } from "@/config/animationConfig";

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

export const useMobileAppShowcase = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLDivElement>(null);
  const appLogoRef = useRef<HTMLDivElement>(null);
  const storeLinksRef = useRef<HTMLDivElement>(null);
  const descriptionRef = useRef<HTMLDivElement>(null);
  const screenshotsRef = useRef<HTMLDivElement>(null);
  const featuresRef = useRef<HTMLDivElement>(null);
  const travelOptionsRef = useRef<HTMLDivElement>(null);

  // State for travel options (kept for compatibility but not used for interactions)
  const [expandedOption] = useState<number | null>(null);
  const [isTransitioning] = useState(false);

  // Enhanced animations with GSAP and ScrollTrigger
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const checkLoadingComplete = () => {
      const loadingScreen = document.querySelector('[data-loading-screen]');
      if (!loadingScreen) {
        initScrollAnimations();
      } else {
        setTimeout(checkLoadingComplete, 100);
      }
    };

    const initScrollAnimations = () => {
      debugLog("Initializing Trego App animations");

      // Set initial states for animations
      gsap.set(".app-title", ANIMATION_CONFIG.INITIAL_STATES.TEXT);
      gsap.set(".app-logo", { opacity: 0, y: 30, scale: 0.8 });
      gsap.set(".app-store-links", { opacity: 0, y: 20 });
      gsap.set(".app-description", ANIMATION_CONFIG.INITIAL_STATES.TEXT);
      gsap.set(".app-screenshot-main", { opacity: 0, y: 50, scale: 0.9 });
      gsap.set(".app-screenshot-additional", { opacity: 0, y: 30, scale: 0.9, stagger: 0.1 });
      gsap.set(".app-feature-card", ANIMATION_CONFIG.INITIAL_STATES.CARDS);
      gsap.set(".travel-option-card", ANIMATION_CONFIG.INITIAL_STATES.CARDS);

      // Set initial states for section titles
      gsap.set(".why-choose-title", ANIMATION_CONFIG.INITIAL_STATES.TEXT);
      gsap.set(".why-choose-description", ANIMATION_CONFIG.INITIAL_STATES.TEXT);
      gsap.set(".travel-way-title", ANIMATION_CONFIG.INITIAL_STATES.TEXT);
      gsap.set(".travel-way-description", ANIMATION_CONFIG.INITIAL_STATES.TEXT);

      // Title animation with scrub
      ScrollTrigger.create({
        trigger: ".app-title",
        start: "top 80%",
        end: "bottom 60%",
        scrub: 0.5,
        animation: gsap.to(".app-title", {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 1,
          ease: "power2.out"
        })
      });

      // App logo animation with scrub
      ScrollTrigger.create({
        trigger: ".app-logo",
        start: "top 75%",
        end: "bottom 55%",
        scrub: 0.5,
        animation: gsap.to(".app-logo", {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 1,
          ease: "power2.out"
        })
      });

      // Store links animation with scrub
      ScrollTrigger.create({
        trigger: ".app-store-links",
        start: "top 70%",
        end: "bottom 50%",
        scrub: 0.5,
        animation: gsap.to(".app-store-links", {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: "power2.out"
        })
      });

      // Description animation with scrub
      ScrollTrigger.create({
        trigger: ".app-description",
        start: "top 70%",
        end: "bottom 50%",
        scrub: 0.5,
        animation: gsap.to(".app-description", {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: "power2.out"
        })
      });

      // Main screenshot animation with scrub
      ScrollTrigger.create({
        trigger: ".app-screenshot-main",
        start: "top 70%",
        end: "center 40%",
        scrub: 0.7,
        animation: gsap.to(".app-screenshot-main", {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 1.2,
          ease: "power2.out"
        })
      });

      // Additional screenshots animation with scrub
      ScrollTrigger.create({
        trigger: ".app-screenshot-additional",
        start: "top 75%",
        end: "bottom 45%",
        scrub: 0.7,
        animation: gsap.to(".app-screenshot-additional", {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 1,
          ease: "power2.out"
        })
      });

      // Feature cards animation with scrub
      ScrollTrigger.create({
        trigger: ".app-feature-card",
        start: "top 80%",
        end: "bottom 50%",
        scrub: 0.6,
        animation: gsap.to(".app-feature-card", {
          opacity: 1,
          y: 0,
          scale: 1,
          stagger: 0.1,
          duration: 1,
          ease: "power2.out"
        })
      });

      // Why Choose Trego title animation with scrub
      ScrollTrigger.create({
        trigger: ".why-choose-title",
        start: "top 85%",
        end: "bottom 55%",
        scrub: 0.5,
        animation: gsap.to(".why-choose-title", {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 1,
          ease: "power2.out"
        })
      });

      // Why Choose Trego description animation with scrub
      ScrollTrigger.create({
        trigger: ".why-choose-description",
        start: "top 80%",
        end: "bottom 50%",
        scrub: 0.5,
        animation: gsap.to(".why-choose-description", {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 1,
          ease: "power2.out"
        })
      });

      // Travel Your Way title animation with scrub
      ScrollTrigger.create({
        trigger: ".travel-way-title",
        start: "top 85%",
        end: "bottom 55%",
        scrub: 0.5,
        animation: gsap.to(".travel-way-title", {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 1,
          ease: "power2.out"
        })
      });

      // Travel Your Way description animation with scrub
      ScrollTrigger.create({
        trigger: ".travel-way-description",
        start: "top 80%",
        end: "bottom 50%",
        scrub: 0.5,
        animation: gsap.to(".travel-way-description", {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 1,
          ease: "power2.out"
        })
      });

      // Travel option cards animation with scrub
      ScrollTrigger.create({
        trigger: ".travel-option-card",
        start: "top 80%",
        end: "bottom 50%",
        scrub: 0.6,
        animation: gsap.to(".travel-option-card", {
          opacity: 1,
          y: 0,
          scale: 1,
          stagger: 0.15,
          duration: 1,
          ease: "power2.out"
        })
      });
    };

    checkLoadingComplete();

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  // Scroll functionality removed - no longer needed for static cards

  // Travel option functionality removed - cards are now static display elements

  return {
    refs: {
      sectionRef,
      titleRef,
      appLogoRef,
      storeLinksRef,
      descriptionRef,
      screenshotsRef,
      featuresRef,
      travelOptionsRef
    },
    expandedOption,
    isTransitioning
  };
};
