"use client";

import { useEffect, useRef, useState, useCallback } from "react";

interface PerformanceMetrics {
  fps: number;
  frameTime: number;
  scrollEvents: number;
  animationCount: number;
  memoryUsage?: number;
  domNodes: number;
  timestamp: number;
}

interface PerformanceReport {
  averageFPS: number;
  minFPS: number;
  maxFPS: number;
  scrollEventsPerSecond: number;
  activeAnimations: number;
  memoryTrend: 'increasing' | 'stable' | 'decreasing';
  bottlenecks: string[];
  recommendations: string[];
}

export const usePerformanceTracker = (enabled: boolean = true) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics[]>([]);
  const [report, setReport] = useState<PerformanceReport | null>(null);
  const [isTracking, setIsTracking] = useState(false);
  
  const frameTimesRef = useRef<number[]>([]);
  const lastFrameTimeRef = useRef<number>(performance.now());
  const scrollCountRef = useRef<number>(0);
  const animationFrameRef = useRef<number | null>(null);

  const scrollListenerRef = useRef<(() => void) | null>(null);

  // Track scroll events
  const trackScrollEvents = () => {
    scrollCountRef.current++;
  };

  // Measure current performance
  const measurePerformance = (): PerformanceMetrics => {
    const now = performance.now();
    const frameTime = now - lastFrameTimeRef.current;
    lastFrameTimeRef.current = now;

    // Calculate FPS
    frameTimesRef.current.push(frameTime);
    if (frameTimesRef.current.length > 60) { // Keep last 60 frames
      frameTimesRef.current.shift();
    }
    
    const avgFrameTime = frameTimesRef.current.reduce((a, b) => a + b, 0) / frameTimesRef.current.length;
    const fps = 1000 / avgFrameTime;

    // Count DOM nodes
    const domNodes = document.querySelectorAll('*').length;

    // Get memory usage if available
    let memoryUsage: number | undefined;
    if ('memory' in performance) {
      const memInfo = (performance as { memory: { usedJSHeapSize: number } }).memory;
      memoryUsage = memInfo.usedJSHeapSize / (1024 * 1024); // Convert to MB
    }

    // Count active animations (approximate)
    const animationCount = document.querySelectorAll('[style*="animation"], [style*="transition"]').length;

    return {
      fps: Math.round(fps),
      frameTime: avgFrameTime,
      scrollEvents: scrollCountRef.current,
      animationCount,
      memoryUsage,
      domNodes,
      timestamp: now
    };
  };

  // Generate performance report
  const generateReport = (metricsData: PerformanceMetrics[]): PerformanceReport => {
    if (metricsData.length === 0) {
      return {
        averageFPS: 0,
        minFPS: 0,
        maxFPS: 0,
        scrollEventsPerSecond: 0,
        activeAnimations: 0,
        memoryTrend: 'stable',
        bottlenecks: [],
        recommendations: []
      };
    }

    const fps = metricsData.map(m => m.fps);
    const averageFPS = fps.reduce((a, b) => a + b, 0) / fps.length;
    const minFPS = Math.min(...fps);
    const maxFPS = Math.max(...fps);

    // Calculate scroll events per second
    const timeSpan = (metricsData[metricsData.length - 1].timestamp - metricsData[0].timestamp) / 1000;
    const totalScrollEvents = metricsData[metricsData.length - 1].scrollEvents;
    const scrollEventsPerSecond = totalScrollEvents / timeSpan;

    // Memory trend analysis
    const memoryValues = metricsData.filter(m => m.memoryUsage).map(m => m.memoryUsage!);
    let memoryTrend: 'increasing' | 'stable' | 'decreasing' = 'stable';
    if (memoryValues.length > 1) {
      const firstHalf = memoryValues.slice(0, Math.floor(memoryValues.length / 2));
      const secondHalf = memoryValues.slice(Math.floor(memoryValues.length / 2));
      const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
      const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;
      
      if (secondAvg > firstAvg * 1.1) memoryTrend = 'increasing';
      else if (secondAvg < firstAvg * 0.9) memoryTrend = 'decreasing';
    }

    // Identify bottlenecks
    const bottlenecks: string[] = [];
    const recommendations: string[] = [];

    if (averageFPS < 30) {
      bottlenecks.push('Low FPS (< 30)');
      recommendations.push('Reduce animation complexity or particle count');
    }
    
    if (scrollEventsPerSecond > 100) {
      bottlenecks.push('High scroll event frequency');
      recommendations.push('Implement scroll throttling or debouncing');
    }

    const lastMetric = metricsData[metricsData.length - 1];
    if (lastMetric.animationCount > 50) {
      bottlenecks.push('Too many active animations');
      recommendations.push('Batch animations or reduce concurrent animations');
    }

    if (lastMetric.domNodes > 5000) {
      bottlenecks.push('High DOM node count');
      recommendations.push('Optimize DOM structure or implement virtualization');
    }

    if (memoryTrend === 'increasing') {
      bottlenecks.push('Memory leak detected');
      recommendations.push('Check for proper cleanup of event listeners and animations');
    }

    return {
      averageFPS: Math.round(averageFPS),
      minFPS,
      maxFPS,
      scrollEventsPerSecond: Math.round(scrollEventsPerSecond),
      activeAnimations: lastMetric.animationCount,
      memoryTrend,
      bottlenecks,
      recommendations
    };
  };

  // Start tracking
  const startTracking = () => {
    if (!enabled || isTracking) return;
    
    setIsTracking(true);
    setMetrics([]);
    scrollCountRef.current = 0;
    frameTimesRef.current = [];

    // Add scroll listener
    scrollListenerRef.current = trackScrollEvents;
    window.addEventListener('scroll', scrollListenerRef.current, { passive: true });

    // Start measuring performance
    const measure = () => {
      const metric = measurePerformance();
      setMetrics(prev => [...prev, metric]);
      animationFrameRef.current = requestAnimationFrame(measure);
    };
    animationFrameRef.current = requestAnimationFrame(measure);

    console.log('🔍 Performance tracking started');
  };

  // Stop tracking
  const stopTracking = useCallback(() => {
    if (!isTracking) return;
    
    setIsTracking(false);

    // Remove scroll listener
    if (scrollListenerRef.current) {
      window.removeEventListener('scroll', scrollListenerRef.current);
    }

    // Stop measuring
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }

    // Generate final report
    setMetrics(currentMetrics => {
      const finalReport = generateReport(currentMetrics);
      setReport(finalReport);
      
      // Log report to console
      console.log('📊 Performance Report:', finalReport);
      console.log('🎯 Bottlenecks:', finalReport.bottlenecks);
      console.log('💡 Recommendations:', finalReport.recommendations);
      
      return currentMetrics;
    });

    console.log('⏹️ Performance tracking stopped');
  }, [isTracking]);

  // Clear data
  const clearData = () => {
    setMetrics([]);
    setReport(null);
    scrollCountRef.current = 0;
    frameTimesRef.current = [];
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (isTracking) {
        stopTracking();
      }
    };
  }, [isTracking, stopTracking]);

  return {
    metrics,
    report,
    isTracking,
    startTracking,
    stopTracking,
    clearData,
    currentFPS: frameTimesRef.current.length > 0 ? 
      Math.round(1000 / (frameTimesRef.current.reduce((a, b) => a + b, 0) / frameTimesRef.current.length)) : 0
  };
};
