"use client";

import { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Register ScrollTrigger plugin
if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

export const useScrollRotation = () => {
  const modelRef = useRef<HTMLDivElement>(null);
  const rotationRef = useRef({ x: 0, y: 0, z: 0 });

  useEffect(() => {
    const model = modelRef.current;
    if (!model) return;

    // Create scroll-triggered rotation animation
    const scrollTrigger = ScrollTrigger.create({
      trigger: model,
      start: "top bottom",
      end: "bottom top",
      scrub: 1, // Smooth scrubbing
      onUpdate: (self) => {
        const progress = self.progress;

        // Calculate rotation based on scroll progress
        rotationRef.current.y = progress * 360; // Full rotation on Y axis
        rotationRef.current.x = Math.sin(progress * Math.PI * 2) * 15; // Subtle X wobble
        rotationRef.current.z = Math.cos(progress * Math.PI * 4) * 5; // Subtle Z tilt

        // Apply rotation to the model
        gsap.set(model, {
          rotationY: rotationRef.current.y,
          rotationX: rotationRef.current.x,
          rotationZ: rotationRef.current.z,
          transformOrigin: "center center",
        });
      },
    });

    // Additional hover rotation effect
    const handleMouseMove = (e: MouseEvent) => {
      const rect = model.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;

      const deltaX = (e.clientX - centerX) / rect.width;
      const deltaY = (e.clientY - centerY) / rect.height;

      gsap.to(model, {
        rotationY: rotationRef.current.y + deltaX * 20,
        rotationX: rotationRef.current.x - deltaY * 20,
        duration: 0.3,
        ease: "power2.out",
      });
    };

    const handleMouseLeave = () => {
      gsap.to(model, {
        rotationY: rotationRef.current.y,
        rotationX: rotationRef.current.x,
        rotationZ: rotationRef.current.z,
        duration: 0.5,
        ease: "power2.out",
      });
    };

    model.addEventListener("mousemove", handleMouseMove);
    model.addEventListener("mouseleave", handleMouseLeave);

    return () => {
      scrollTrigger.kill();
      model.removeEventListener("mousemove", handleMouseMove);
      model.removeEventListener("mouseleave", handleMouseLeave);
    };
  }, []);

  return { modelRef };
};
