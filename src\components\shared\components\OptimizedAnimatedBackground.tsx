"use client";

import { useEffect, useState } from "react";

export default function OptimizedAnimatedBackground() {
  const [isWhiteSection, setIsWhiteSection] = useState(false);

  // ULTRA-PERFORMANCE: Use Intersection Observer with smoother transition
  useEffect(() => {
    const mobileAppSection = document.getElementById('mobile-app-showcase');
    if (!mobileAppSection) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        // More gradual transition - start transitioning earlier
        setIsWhiteSection(entry.isIntersecting);
      },
      {
        threshold: 0.1, // Start transition when 10% of the section is visible
        rootMargin: '200px 0px -10% 0px' // Much earlier trigger for smoother transition
      }
    );

    observer.observe(mobileAppSection);

    return () => {
      observer.disconnect();
    };
  }, []);

  return (
    <>
      {/* EXTREME PERFORMANCE CSS - 120+ FPS TARGET */}
      <style jsx>{`
        @keyframes extremeMorph {
          0% { background-position: 0% 0%; }
          100% { background-position: 100% 100%; }
        }

        .extreme-gpu {
          will-change: auto;
          transform: translateZ(0);
          contain: strict;
          isolation: isolate;
        }
      `}</style>

      {/* ULTRA-PERFORMANCE ANIMATED BACKGROUND */}
      <div className="fixed inset-0 z-0 pointer-events-none overflow-hidden">

        {/* BACKGROUND VIDEO WITH SMOOTH WHITE TRANSITION SYSTEM */}
        <div className="absolute inset-0">
          {/* Dark Background with Video */}
          <div
            className={`absolute inset-0 transition-all duration-[1500ms] ease-in-out ${
              isWhiteSection ? 'opacity-0 scale-105' : 'opacity-100 scale-100'
            }`}
          >
            {/* Background Video - COMMENTED OUT FOR WHITE BACKGROUND TRANSITION */}
            {/* <video
              ref={(video) => {
                // if (video) {
                //   video.playbackRate = 0.1; // 4x slower - ULTRA SLOW (supported range)
                // }
              }}
              autoPlay
              loop
              muted
              playsInline
              className="absolute inset-0 w-full h-full object-cover opacity-60"
              style={{ filter: 'hue-rotate(280deg) saturate(1.1)' }}
            >
              <source src="/assets/landing/WhiteBackground.webm" type="video/webm" />
            </video> */}
            {/* Dark overlay for depth with smooth transition */}
            <div className={`absolute inset-0 bg-gradient-to-br from-black/50 via-[#302cff]/30 to-black/50 transition-opacity duration-[1500ms] ease-in-out ${
              isWhiteSection ? 'opacity-0' : 'opacity-100'
            }`}></div>
          </div>

          {/* White Background Layer with smooth fade-in - PARTIAL OVERLAY */}
          <div
            className={`absolute inset-0 transition-all duration-[1500ms] ease-in-out ${
              isWhiteSection ? 'opacity-85 scale-100' : 'opacity-0 scale-95'
            }`}
            style={{
              background: 'linear-gradient(135deg, rgba(250, 250, 250, 0.9) 0%, rgba(243, 232, 255, 0.85) 50%, rgba(250, 250, 250, 0.9) 100%)'
            }}
          />

          {/* Intermediate transition layer for ultra-smooth blending */}
          <div
            className={`absolute inset-0 transition-opacity duration-[1200ms] ease-in-out ${
              isWhiteSection ? 'opacity-20' : 'opacity-0'
            }`}
            style={{
              background: 'radial-gradient(circle at center, rgba(255, 255, 255, 0.6) 0%, rgba(243, 232, 255, 0.4) 50%, rgba(255, 255, 255, 0.3) 100%)'
            }}
          />
        </div>

        {/* SINGLE MINIMAL ANIMATION - MAXIMUM PERFORMANCE */}
        <div
          className={`absolute inset-0 transition-all duration-[1500ms] ease-in-out extreme-gpu ${
            isWhiteSection ? 'opacity-5' : 'opacity-15'
          }`}
          style={{
            background: 'radial-gradient(circle at 40% 50%, rgba(147, 51, 234, 0.12) 0%, transparent 40%)',
            backgroundSize: '600px 600px',
            animation: 'extremeMorph 60s linear infinite'
          }}
        />
      </div>
    </>
  );
}
