export const APP_STORE_LINKS = {
  apple: "https://apps.apple.com/eg/app/trego/id6742484811",
  google: "https://play.google.com/store/apps/details?id=com.TregoTech.TregoApp"
};

export const APP_SCREENSHOTS = [
  { src: "/assets/trego_app/IMG_0224.webp", alt: "Trego App Home Screen", shadow: "hover:shadow-blue-500/30" },
  { src: "/assets/trego_app/IMG_0225.webp", alt: "Search Transportation", shadow: "hover:shadow-green-500/30" },
  { src: "/assets/trego_app/IMG_0226.webp", alt: "Route Planning", shadow: "hover:shadow-yellow-500/30" },
  { src: "/assets/trego_app/IMG_0227.webp", alt: "Booking Interface", shadow: "hover:shadow-red-500/30" }
];

export const APP_FEATURES = [
  {
    icon: "🔒",
    title: "Security",
    description: "Bank-level encryption and secure payment processing",
    bgColor: "bg-gray-800/60",
    textColor: "text-white",
    iconBg: "bg-purple-500/30"
  },
  {
    icon: "✨",
    title: "Seamless Experience",
    description: "Intuitive design and smooth user interface",
    bgColor: "bg-gray-800/60",
    textColor: "text-white",
    iconBg: "bg-purple-500/30"
  },
  {
    icon: "🎯",
    title: "Easy Trip",
    description: "Simple trip planning with real-time updates",
    bgColor: "bg-gray-800/60",
    textColor: "text-white",
    iconBg: "bg-purple-500/30"
  },
  {
    icon: "📱",
    title: "Easy Booking",
    description: "Quick and hassle-free booking process",
    bgColor: "bg-gray-800/60",
    textColor: "text-white",
    iconBg: "bg-purple-500/30"
  },
  {
    icon: "🚀",
    title: "Quick Search",
    description: "Lightning-fast search with comprehensive results",
    bgColor: "bg-gray-800/60",
    textColor: "text-white",
    iconBg: "bg-[#302cff]/30"
  },
  {
    icon: "🌟",
    title: "Premium Support",
    description: "24/7 customer support for all your needs",
    bgColor: "bg-gray-800/60",
    textColor: "text-white",
    iconBg: "bg-[#302cff]/30"
  }
];

export const TRAVEL_OPTIONS = [
  {
    icon: "🚌",
    title: "Bus Travel",
    description: "Comfortable and affordable bus journeys across Egypt",
    color: "from-blue-400 to-blue-600",
    bgColor: "bg-blue-500/10",
    borderColor: "border-blue-400/30",
    detailedInfo: {
      image: "/assets/bus.webp",
      features: [
        "🎫 Easy online booking with instant confirmation",
        "🗺️ Extensive route network covering all major cities",
        "💺 Choose from economy, business, and VIP seating",
        "📱 Real-time tracking and live updates",
        "💳 Secure payment options including mobile wallets",
        "🎧 24/7 customer support in Arabic and English"
      ],
      benefits: "Save up to 40% compared to traditional booking methods while enjoying premium comfort and reliability.",
      stats: {
        routes: "500+",
        cities: "50+",
        satisfaction: "98%"
      }
    }
  },
  {
    icon: "✈️",
    title: "Flight Booking",
    description: "Quick and convenient domestic and international flights",
    color: "from-[#302cff] to-[#1e1a99]",
    bgColor: "bg-[#302cff]/10",
    borderColor: "border-[#302cff]/30",
    detailedInfo: {
      image: "/assets/planes.webp",
      features: [
        "✈️ Domestic and international flight options",
        "💰 Best price guarantee with price comparison",
        "🎯 Smart filters for airlines, times, and prices",
        "📋 Digital boarding passes and e-tickets",
        "🔄 Free cancellation and easy rebooking",
        "🏆 Partnerships with major airlines worldwide"
      ],
      benefits: "Access exclusive deals and enjoy seamless booking experience with our advanced flight search technology.",
      stats: {
        airlines: "200+",
        destinations: "1000+",
        savings: "35%"
      }
    }
  },
  {
    icon: "🚂",
    title: "Train Routes",
    description: "Scenic and relaxing train journeys throughout the country",
    color: "from-green-400 to-green-600",
    bgColor: "bg-green-500/10",
    borderColor: "border-green-400/30",
    detailedInfo: {
      image: "/assets/train.webp",
      features: [
        "🚄 High-speed and regular train services",
        "🌅 Scenic routes with breathtaking views",
        "🛏️ Sleeper cabins for overnight journeys",
        "🍽️ Onboard dining and refreshment services",
        "📶 Free WiFi and charging stations",
        "🎫 Flexible ticket options and group discounts"
      ],
      benefits: "Experience Egypt's beautiful landscapes while traveling in comfort with our comprehensive train network.",
      stats: {
        stations: "100+",
        routes: "25+",
        comfort: "95%"
      }
    }
  }
];

export const ANIMATION_DELAYS = {
  FALLBACK_CARDS: 2000,
  FINAL_FALLBACK: 3000
};

export const APP_DESCRIPTION = {
  main: "Your intelligent travel companion for seamless transportation across Egypt.",
  subtitle: "Discover routes, book tickets, and travel with confidence - all in one powerful app."
};

export const SECTION_TITLES = {
  whyChoose: "Why Choose Trego?",
  whyChooseDescription: "Experience the future of travel with our innovative features designed for your convenience",
  travelWay: "Travel Your Way",
  travelWayDescription: "Choose from multiple transportation options to reach your destination with ease and comfort",
  callToAction: "Ready to start your journey?",
  callToActionSubtext: "Download the app and explore all options"
};
